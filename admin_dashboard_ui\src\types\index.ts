// ============================================================================
// ENTERPRISE ROLE-BASED ACCESS CONTROL (RBAC)
// ============================================================================

export enum Roles {
  SUPERADMIN = 'superadmin',
  ADMIN = 'admin',
  SUPPORT_AGENT = 'support_agent',
  COMPLIANCE_AUDITOR = 'compliance_auditor',
  DEVELOPER = 'developer',
  HR_LEAD = 'hr_lead',
  VIEWER = 'viewer'
}

export type Role = keyof typeof Roles | `${Lowercase<keyof typeof Roles>}`;

export interface Permission {
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'execute';
  conditions?: Record<string, any>;
}

export interface RolePermissions {
  role: Role;
  permissions: Permission[];
  inherits?: Role[];
}

// ============================================================================
// USER MANAGEMENT & AUTHENTICATION
// ============================================================================

export interface AdminUser {
  id: string;
  email: string;
  role: Role;
  active: boolean;
  full_name: string;
  name?: string; // alias for compatibility
  avatar_url?: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  last_login?: string;
  two_factor_enabled: boolean;
  backup_codes?: string[];
  preferences: UserPreferences;
  invited_by?: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  dashboard_layout?: DashboardLayout;
}

export interface NotificationSettings {
  email_alerts: boolean;
  push_notifications: boolean;
  security_alerts: boolean;
  system_updates: boolean;
  escalation_alerts: boolean;
}

export interface DashboardLayout {
  widgets: WidgetConfig[];
  layout: 'grid' | 'list';
  refresh_interval: number;
}

export interface WidgetConfig {
  id: string;
  type: string;
  position: { x: number; y: number; w: number; h: number };
  config: Record<string, any>;
}

// ============================================================================
// ORGANIZATION & MULTI-TENANCY
// ============================================================================

export interface Organization {
  id: string;
  name: string;
  slug: string;
  domain: string;
  plan: 'starter' | 'professional' | 'enterprise';
  settings: OrganizationSettings;
  created_at: string;
  updated_at: string;
  owner_id: string;
  billing_info?: BillingInfo;
}

export interface OrganizationSettings {
  branding: {
    logo_url?: string;
    primary_color: string;
    secondary_color: string;
  };
  security: {
    enforce_2fa: boolean;
    session_timeout: number;
    password_policy: PasswordPolicy;
    ip_whitelist?: string[];
  };
  features: {
    api_access: boolean;
    webhooks: boolean;
    advanced_analytics: boolean;
    custom_integrations: boolean;
  };
}

export interface PasswordPolicy {
  min_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_symbols: boolean;
  expiry_days?: number;
}

export interface BillingInfo {
  plan: string;
  billing_cycle: 'monthly' | 'yearly';
  next_billing_date: string;
  payment_method: string;
  usage_limits: UsageLimits;
}

export interface UsageLimits {
  max_users: number;
  max_api_calls: number;
  max_storage_gb: number;
  current_usage: {
    users: number;
    api_calls: number;
    storage_gb: number;
  };
}

// ============================================================================
// AUDIT & COMPLIANCE
// ============================================================================

export interface AuditLog {
  id: string;
  timestamp: string;
  organization_id: string;
  actor_id: string;
  actor_email: string;
  admin_email?: string; // alias for compatibility
  action: string;
  resource_type: string;
  resource_id?: string;
  ip_address: string;
  user_agent: string;
  location?: GeoLocation;
  metadata: Record<string, any>;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  compliance_tags?: string[];
}

export interface GeoLocation {
  country: string;
  region: string;
  city: string;
  latitude?: number;
  longitude?: number;
}

export interface DeviceLog {
  id: string;
  user_id: string;
  email: string;
  device_fingerprint: string;
  user_agent: string;
  ip_address: string;
  first_seen: string;
  last_seen: string;
  location?: GeoLocation;
  country?: string;
  region?: string;
  city?: string;
  is_trusted: boolean;
  risk_score: number;
  sessions_count: number;
}

// ============================================================================
// CHATBOT METRICS & ANALYTICS
// ============================================================================

export interface ChatbotMetrics {
  total_queries: number;
  total_queries_change: number;
  active_users: number;
  active_users_change: number;
  avg_sentiment: number;
  unique_questions: number;
  chats_per_day: number[];
  avg_chat_duration: number;
  avg_chat_duration_change: number;
  resolution_rate: number;
  resolution_rate_change: number;
  escalation_rate: number;
  escalation_rate_change: number;
  user_satisfaction: number;
  user_satisfaction_change: number;
  top_intents: IntentMetric[];
  response_times: ResponseTimeMetric[];
  error_rate: number;
  error_rate_change: number;
  uptime_percentage: number;
  uptime_change: number;
}

export interface IntentMetric {
  intent: string;
  count: number;
  confidence_avg: number;
  success_rate: number;
}

export interface ResponseTimeMetric {
  timestamp: string;
  avg_response_time: number;
  p95_response_time: number;
  p99_response_time: number;
}

export interface ChatSession {
  id: string;
  user_id: string;
  started_at: string;
  ended_at?: string;
  message_count: number;
  duration_seconds?: number;
  satisfaction_score?: number;
  escalated: boolean;
  escalation_reason?: string;
  resolved: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

export interface ChatMessage {
  id: string;
  session_id: string;
  sender: 'user' | 'bot' | 'agent';
  content: string;
  timestamp: string;
  intent?: string;
  confidence?: number;
  entities?: Entity[];
  sentiment?: SentimentAnalysis;
  response_time?: number;
}

export interface Entity {
  type: string;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

export interface SentimentAnalysis {
  score: number; // -1 to 1
  label: 'positive' | 'neutral' | 'negative';
  confidence: number;
}

// ============================================================================
// FEATURE REQUESTS & FEEDBACK
// ============================================================================

export interface FeatureRequest {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'submitted' | 'under_review' | 'planned' | 'in_progress' | 'completed' | 'rejected';
  category: string;
  submitted_by: string;
  submitted_at: string;
  updated_at: string;
  votes: number;
  comments: FeatureComment[];
  estimated_effort?: string;
  target_release?: string;
  tags: string[];
}

export interface FeatureComment {
  id: string;
  author: string;
  content: string;
  timestamp: string;
  type: 'comment' | 'status_update' | 'internal_note';
}

// ============================================================================
// API MANAGEMENT & WEBHOOKS
// ============================================================================

export interface ApiKey {
  id: string;
  name: string;
  key_preview: string; // Only first 8 chars + ...
  permissions: string[];
  created_at: string;
  last_used?: string;
  expires_at?: string;
  is_active: boolean;
  usage_stats: ApiKeyUsage;
}

export interface ApiKeyUsage {
  total_requests: number;
  requests_today: number;
  rate_limit: number;
  rate_limit_remaining: number;
  rate_limit_reset: string;
}

export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret: string;
  is_active: boolean;
  created_at: string;
  last_triggered?: string;
  success_rate: number;
  retry_policy: WebhookRetryPolicy;
  headers?: Record<string, string>;
}

export interface WebhookRetryPolicy {
  max_retries: number;
  retry_delay: number;
  backoff_multiplier: number;
}

export interface WebhookDelivery {
  id: string;
  webhook_id: string;
  event_type: string;
  payload: Record<string, any>;
  response_status?: number;
  response_body?: string;
  delivered_at: string;
  attempts: number;
  success: boolean;
}

// ============================================================================
// SYSTEM HEALTH & MONITORING
// ============================================================================

export interface SystemHealth {
  overall_status: 'healthy' | 'degraded' | 'down';
  services: ServiceHealth[];
  last_updated: string;
  uptime_percentage: number;
  response_time_avg: number;
}

export interface ServiceHealth {
  name: string;
  status: 'healthy' | 'degraded' | 'down';
  response_time: number;
  error_rate: number;
  last_check: string;
  dependencies: string[];
}

export interface PerformanceMetric {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_io: number;
  active_connections: number;
  queue_size: number;
}

// ============================================================================
// ANOMALY DETECTION & ALERTS
// ============================================================================

export interface Anomaly {
  id: string;
  type: 'traffic_spike' | 'traffic_drop' | 'error_rate_increase' | 'response_time_increase' | 'sentiment_drop';
  severity: 'low' | 'medium' | 'high' | 'critical';
  detected_at: string;
  description: string;
  affected_metrics: string[];
  threshold_value: number;
  actual_value: number;
  confidence: number;
  status: 'active' | 'acknowledged' | 'resolved' | 'false_positive';
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolution_notes?: string;
}

export interface Alert {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  read: boolean;
  action_url?: string;
  action_label?: string;
  expires_at?: string;
}

// ============================================================================
// INTERNATIONALIZATION & LOCALIZATION
// ============================================================================

export interface Language {
  code: string;
  name: string;
  native_name: string;
  rtl: boolean;
  flag_emoji: string;
  completion_percentage: number;
}

export interface LocalizationConfig {
  default_language: string;
  supported_languages: Language[];
  fallback_language: string;
  auto_detect: boolean;
  date_format: string;
  time_format: string;
  number_format: string;
  currency: string;
}

// ============================================================================
// EXPORT & REPORTING
// ============================================================================

export interface ExportRequest {
  id: string;
  type: 'csv' | 'json' | 'xlsx' | 'pdf';
  resource: string;
  filters: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  download_url?: string;
  expires_at?: string;
  file_size?: number;
  requested_by: string;
}

export interface Report {
  id: string;
  name: string;
  description: string;
  type: 'scheduled' | 'on_demand';
  schedule?: ReportSchedule;
  recipients: string[];
  format: 'pdf' | 'csv' | 'xlsx';
  template: string;
  filters: Record<string, any>;
  last_generated?: string;
  next_generation?: string;
  is_active: boolean;
}

export interface ReportSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  day_of_week?: number;
  day_of_month?: number;
  time: string;
  timezone: string;
}

// ============================================================================
// UI/UX TYPES
// ============================================================================

export interface LoadingState {
  isLoading: boolean;
  error?: string;
  lastUpdated?: string;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface FilterState {
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  status?: string[];
  tags?: string[];
  [key: string]: any;
}

export interface SortState {
  field: string;
  direction: 'asc' | 'desc';
}

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, row: any) => React.ReactNode;
}

// ============================================================================
// THEME & STYLING
// ============================================================================

export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  border_radius: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  font_family: string;
  font_size: 'sm' | 'md' | 'lg';
  animations_enabled: boolean;
  high_contrast: boolean;
  reduced_motion: boolean;
}

// ============================================================================
// WORKFLOW & AUTOMATION
// ============================================================================

export interface Workflow {
  id: string;
  name: string;
  description: string;
  trigger: WorkflowTrigger;
  actions: WorkflowAction[];
  conditions: WorkflowCondition[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_executed?: string;
  execution_count: number;
  success_rate: number;
}

export interface WorkflowTrigger {
  type: 'webhook' | 'schedule' | 'event' | 'manual';
  config: Record<string, any>;
}

export interface WorkflowAction {
  id: string;
  type: 'email' | 'webhook' | 'api_call' | 'notification' | 'data_update';
  config: Record<string, any>;
  order: number;
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  logic: 'and' | 'or';
}