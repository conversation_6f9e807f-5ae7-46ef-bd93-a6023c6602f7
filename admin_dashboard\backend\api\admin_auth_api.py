from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends, status, Security, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
from fastapi import Path
from typing import Optional, List
import random, sqlite3, os
from datetime import datetime, timedelta
from ..utils.email_service import EmailService
import jwt
import pyotp
import qrcode
import base64
from io import BytesIO
import sys
import json
from functools import wraps
from fastapi.middleware.cors import CORSMiddleware
import smtplib
from email.mime.text import MIMEText
import requests
from fastapi import Header
from fastapi import APIRouter
from ..utils.ip_geo import get_geo_from_ip
from fastapi import Query
from fastapi import Body
from dotenv import load_dotenv
load_dotenv(dotenv_path="admin_dashboard/.env", override=True)
from fastapi import UploadFile, File, Form
import tempfile

# Stub for SessionModel
class SessionModel:
    def get_active_sessions(self, user_type="admin"):
        return []
    
    def get_session_history(self, user_type="admin", limit=10, offset=0, filters=None):
        return []

app = FastAPI()

# CORS middleware must be the first middleware added
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3002", "http://localhost:3001", "http://localhost:3000"],  # Added 3002 for new frontend port
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)



OTP_EXPIRY_SECONDS = 300
OTP_LENGTH = 6
OTP_RESEND_COOLDOWN = 45
JWT_SECRET = os.getenv('JWT_SECRET', 'changeme')
JWT_ALGORITHM = 'HS256'

ADMIN_DB_PATH = 'admin_dashboard/data/admin_users.db'

def log_action(action):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            request: Request = kwargs.get('request')
            payload = kwargs.get('payload')
            # Try to extract from args if not in kwargs
            if not request:
                for arg in args:
                    if isinstance(arg, Request):
                        request = arg
            if not payload:
                for arg in args:
                    if isinstance(arg, dict) and 'email' in arg:
                        payload = arg
            ip = request.client.host if request else None
            user_agent = request.headers.get('user-agent') if request else None
            email = None
            if payload and isinstance(payload, dict):
                email = payload.get('email')
            elif request:
                # Try to extract from JWT if present
                auth = request.headers.get('authorization')
                if auth and auth.startswith('Bearer '):
                    token = auth.split(' ')[1]
                    jwt_payload = verify_jwt(token)
                    if jwt_payload:
                        email = jwt_payload.get('email')
            # Device fingerprinting: log device info on login/OTP use
            new_device = False
            geo = {"country": None, "region": None, "city": None}
            if email and user_agent and ip and action in ['otp_login', 'otp_resend', 'totp_setup']:
                device_model = DeviceLogModel()
                with device_model._get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('''SELECT id FROM device_log WHERE email = ? AND user_agent = ? AND ip_address = ?''', (email, user_agent, ip))
                    if not cursor.fetchone():
                        new_device = True
                geo = await get_geo_from_ip(ip)
                await device_model.log_device(email, user_agent, ip, geo.get('country'), geo.get('region'), geo.get('city'))
            # Call the route
            result = await func(*args, **kwargs)
            # Log the action
            AuditLogModel().log(
                admin_email=email or 'unknown',
                action=action,
                ip_address=ip,
                metadata={
                    "path": str(request.url) if request else None,
                    "user_agent": user_agent,
                    "new_device": new_device,
                    "geo": geo
                }
            )
            return result
        return wrapper
    return decorator

class AdminUserModel:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()

    def _get_connection(self):
        return sqlite3.connect(self.db_path)

    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    full_name TEXT NOT NULL,
                    active INTEGER DEFAULT 1,
                    two_fa_secret TEXT,
                    two_fa_secret_admin TEXT,
                    role TEXT DEFAULT 'admin',
                    created_at TIMESTAMP NOT NULL,
                    last_login TIMESTAMP,
                    tenant_id TEXT
                )
            ''')
            cursor.execute("PRAGMA table_info(admin_users)")
            columns = [row[1] for row in cursor.fetchall()]
            if 'two_fa_secret_admin' not in columns:
                cursor.execute('ALTER TABLE admin_users ADD COLUMN two_fa_secret_admin TEXT')
            if 'role' not in columns:
                cursor.execute('ALTER TABLE admin_users ADD COLUMN role TEXT DEFAULT "admin"')
            if 'tenant_id' not in columns:
                cursor.execute('ALTER TABLE admin_users ADD COLUMN tenant_id TEXT')
            conn.commit()

    def get_admin_by_email(self, email):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM admin_users WHERE email = ?', (email,))
            row = cursor.fetchone()
            return dict(zip([d[0] for d in cursor.description], row)) if row else None

    def create_admin(self, email, full_name, two_fa_secret=None, role='admin', tenant_id=None):
        now = datetime.utcnow().isoformat()
        if role:
            role = role.upper()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.execute('''
                    INSERT INTO admin_users (email, full_name, two_fa_secret, created_at, active, role, tenant_id)
                    VALUES (?, ?, ?, ?, 1, ?, ?)
                ''', (email, full_name, two_fa_secret, now, role, tenant_id))
                conn.commit()
                return {'success': True, 'admin_id': cursor.lastrowid}
            except sqlite3.IntegrityError:
                return {'success': False, 'message': 'Admin already exists'}

    def update_last_login(self, admin_id):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE admin_users SET last_login = ? WHERE id = ?', (datetime.utcnow().isoformat(), admin_id))
            conn.commit()

    def update_user(self, user_id, full_name=None, role=None, tenant_id=None):
        """Update admin user fields by user_id. Only updates provided fields."""
        fields = []
        values = []
        if full_name is not None:
            fields.append('full_name = ?')
            values.append(full_name)
        if role is not None:
            role = role.upper()
            fields.append('role = ?')
            values.append(role)
        if tenant_id is not None:
            fields.append('tenant_id = ?')
            values.append(tenant_id)
        if not fields:
            return False  # Nothing to update
        values.append(user_id)
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(f'UPDATE admin_users SET {", ".join(fields)} WHERE id = ?', values)
            conn.commit()
            return cursor.rowcount > 0

    def get_admin_users(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM admin_users')
            rows = cursor.fetchall()
            return [dict(zip([d[0] for d in cursor.description], row)) for row in rows]

# Create default admin user if none exists
def ensure_default_admin():
    admin_model = AdminUserModel()
    admin_model._ensure_table()  # Ensure table exists
    
    # Check if any admin users exist
    existing_admins = admin_model.get_admin_users()
    if not existing_admins:
        # Create default admin user
        default_email = os.getenv('DEFAULT_ADMIN_EMAIL', '<EMAIL>')
        default_name = os.getenv('DEFAULT_ADMIN_NAME', 'System Administrator')
        result = admin_model.create_admin(
            email=default_email,
            full_name=default_name,
            role='superadmin'
        )
        if result['success']:
            print(f"Created default admin user: {default_email}")
        else:
            print(f"Failed to create default admin user: {result['message']}")

# Call this function when the app starts
ensure_default_admin()

# --- Models ---
class OTPRequest(BaseModel):
    email: str

class OTPVerify(BaseModel):
    email: str
    otp: str

class TOTPVerify(BaseModel):
    email: str
    totp: str

class AdminRegisterRequest(BaseModel):
    email: str
    full_name: str
    password: str  # Not stored for now

class AdminUserCreateRequest(BaseModel):
    email: str
    full_name: str
    role: str = 'admin'
    tenant_id: str = None

class AdminUserUpdateRequest(BaseModel):
    full_name: str = None
    role: str = None
    tenant_id: str = None

# --- Utils ---
def ensure_admin_otp_table():
    with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_otps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT NOT NULL,
                otp TEXT NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used INTEGER DEFAULT 0,
                created_at TIMESTAMP NOT NULL,
                requester_ip TEXT
            )
        ''')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_admin_otps_email ON admin_otps(email)')
        cursor.execute('''CREATE TABLE IF NOT EXISTS admin_devices (id INTEGER PRIMARY KEY AUTOINCREMENT, email TEXT, device_info TEXT, first_seen TIMESTAMP)''')
        conn.commit()
ensure_admin_otp_table()

def create_jwt(user):
    payload = {
        'user_id': user['id'],
        'email': user['email'],
        'role': user.get('role', 'admin'),  # Use the real role from DB
        'exp': datetime.utcnow() + timedelta(hours=24)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_jwt(token: str):
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except Exception:
        return None

def rbac_required(allowed_roles):
    def dependency(credentials: HTTPAuthorizationCredentials = Security(HTTPBearer())):
        token = credentials.credentials
        payload = verify_jwt(token)
        if not payload or payload.get('role') not in allowed_roles:
            raise HTTPException(status_code=403, detail='Insufficient permissions')
        return payload
    return dependency

# Add TOTP utility for admin users (self-contained)
class AdminTOTP:
    def __init__(self, secret):
        self.totp = pyotp.TOTP(secret)
    def get_qr_url(self, email):
        return self.totp.provisioning_uri(name=email, issuer_name="Admin Dashboard")
    def verify_code(self, code):
        return self.totp.verify(code, valid_window=1)

class RateLimiter:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()
    def _get_connection(self):
        return sqlite3.connect(self.db_path)
    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS rate_limit (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT,
                    ip_address TEXT,
                    action TEXT,
                    timestamp TEXT
                )
            ''')
            conn.commit()
    def log_attempt(self, email, ip_address, action):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO rate_limit (email, ip_address, action, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (email, ip_address, action, datetime.utcnow().isoformat() + 'Z'))
            conn.commit()
    def check_limits(self, email, ip_address, action, cooldown_seconds=60, max_attempts=5, window_minutes=10):
        now = datetime.utcnow()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # Cooldown: last attempt must be > cooldown_seconds ago
            cursor.execute('''SELECT timestamp FROM rate_limit WHERE email = ? AND ip_address = ? AND action = ? ORDER BY timestamp DESC LIMIT 1''', (email, ip_address, action))
            last = cursor.fetchone()
            if last:
                last_time = datetime.fromisoformat(last[0].replace('Z',''))
                if (now - last_time).total_seconds() < cooldown_seconds:
                    return False, int(cooldown_seconds - (now - last_time).total_seconds())
            # Max attempts in window
            window_start = (now - timedelta(minutes=window_minutes)).isoformat() + 'Z'
            cursor.execute('''SELECT COUNT(*) FROM rate_limit WHERE email = ? AND ip_address = ? AND action = ? AND timestamp >= ?''', (email, ip_address, action, window_start))
            count = cursor.fetchone()[0]
            if count >= max_attempts:
                return False, None
            return True, None

# --- Endpoints ---
# COMMENTED OUT: Email OTP functionality temporarily disabled
# def send_otp_email(email, otp):
#     SMTP_SERVER = os.getenv('SMTP_SERVER')
#     SMTP_PORT = int(os.getenv('SMTP_PORT', 587))
#     SMTP_USERNAME = os.getenv('SMTP_USERNAME')
#     SMTP_PASSWORD = os.getenv('SMTP_PASSWORD')
#     SENDER_EMAIL = os.getenv('SENDER_EMAIL')
#     subject = 'Your Admin OTP'
#     body = f'Your OTP is: {otp}\nIt expires in 5 minutes.'
#     if not all([SMTP_SERVER, SMTP_PORT, SMTP_USERNAME, SMTP_PASSWORD, SENDER_EMAIL]):
#         print(f"[ERROR] Email service not fully configured. Missing SMTP credentials.")
#         return {"success": False, "message": "Email service not fully configured. Contact admin."}
#     try:
#         msg = MIMEText(body)
#         msg['Subject'] = subject
#         msg['From'] = SENDER_EMAIL
#         msg['To'] = email
#         with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
#             server.starttls()
#             server.login(SMTP_USERNAME, SMTP_PASSWORD)
#             server.sendmail(SENDER_EMAIL, [email], msg.as_string())
#         print(f"[INFO] OTP email sent to {email}")
#         AuditLogModel().log(admin_email=email, action='otp_sent_async', ip_address=None, metadata={"otp": "dispatched"})
#         return {"success": True, "message": "OTP sent successfully"}
#     except Exception as e:
#         print(f"[ERROR] Error sending OTP email: {e}")
#         return {"success": False, "message": f"Error sending OTP email: {str(e)}"}

# COMMENTED OUT: OTP request endpoint temporarily disabled
# @app.post('/admin/request-otp')
# @log_action('otp_resend')
# async def admin_request_otp(data: OTPRequest, request: Request, background_tasks: BackgroundTasks):
#     email = data.email
#     user = AdminUserModel().get_admin_by_email(email)
#     if not user or not user.get('active', 1):
#         # Log failed login attempt for non-admin or inactive user
#         ip = request.client.host if request else None
#         user_agent = request.headers.get('user-agent') if request else None
#         FailedLoginModel().log(email, ip, user_agent, reason='not in admin db or inactive')
#         raise HTTPException(status_code=403, detail='Unauthorized')
#     now = datetime.utcnow()
#     requester_ip = request.client.host
#     # Rate limiting
#     limiter = RateLimiter()
#     allowed, retry_after = limiter.check_limits(email, requester_ip, 'otp_resend', cooldown_seconds=60, max_attempts=5, window_minutes=10)
#     if not allowed:
#         if retry_after:
#             raise HTTPException(status_code=429, detail=f'Too many OTP requests. Try again in {retry_after} seconds.')
#         else:
#             raise HTTPException(status_code=429, detail='Too many OTP requests. Try again later.')
#     limiter.log_attempt(email, requester_ip, 'otp_resend')
#     otp = f"{random.randint(0, 999999):06d}"
#     expires_at = now + timedelta(seconds=OTP_EXPIRY_SECONDS)
#     with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
#         cursor = conn.cursor()
#         cursor.execute('''INSERT INTO admin_otps (email, otp, expires_at, used, created_at, requester_ip) VALUES (?, ?, ?, 0, ?, ?)''',
#                        (email, otp, expires_at.isoformat(), now.isoformat(), requester_ip))
#         conn.commit()
#     # Async OTP email dispatch
#     background_tasks.add_task(send_otp_email, email, otp)
#     return JSONResponse(content={"success": True, "message": "OTP sent to email (async)"})

# COMMENTED OUT: API OTP request endpoint temporarily disabled
# @app.post('/api/auth/request-otp')
# async def api_auth_request_otp(data: OTPRequest, request: Request, background_tasks: BackgroundTasks):
#     try:
#         return await admin_request_otp(data, request, background_tasks)
#     except Exception as e:
#         # Always return a JSON error response
#         return JSONResponse(status_code=500, content={"success": False, "message": f"Internal server error: {str(e)}"})

# COMMENTED OUT: OTP verification endpoint temporarily disabled
# @app.post('/admin/verify-otp')
# @log_action('otp_login')
# async def admin_verify_otp(data: OTPVerify, request: Request):
#     email = data.email
#     otp = data.otp
#     print(f"DEBUG: Verifying OTP for {email} with code {otp}")
#     user = AdminUserModel().get_admin_by_email(email)
#     if not user or not user.get('active', 1):
#         print("DEBUG: User not found or not active")
#         raise HTTPException(status_code=403, detail='Unauthorized')
#     now = datetime.utcnow()
#     with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
#         cursor = conn.cursor()
#         cursor.execute('''SELECT id, expires_at, used FROM admin_otps WHERE email = ? AND otp = ? ORDER BY created_at DESC LIMIT 1''', (email, otp))
#         row = cursor.fetchone()
#         row = cursor.fetchone()
#         print(f"DEBUG: OTP DB row: {row}")
#         if not row:
#             print("DEBUG: OTP not found in DB")
#             raise HTTPException(status_code=401, detail='Invalid OTP')
#         otp_id, expires_at_str, used = row
#         expires_at = datetime.fromisoformat(expires_at_str)
#         if used:
#             print("DEBUG: OTP already used")
#             raise HTTPException(status_code=401, detail='OTP already used')
#         if now > expires_at:
#             print("DEBUG: OTP expired")
#             raise HTTPException(status_code=401, detail='OTP expired')
#         cursor.execute('UPDATE admin_otps SET used = 1 WHERE id = ?', (otp_id,))
#         conn.commit()
#     # Issue short-lived JWT for TOTP step
#     token = create_jwt(user)
#     # --- Create session record for admin user ---
#     try:
#         import uuid
#         session_id = str(uuid.uuid4())
#         ip_address = request.client.host if request else None
#         user_agent = request.headers.get('user-agent') if request else None
#         browser = None
#         os = None
#         device_fingerprint = None
#         login_time = datetime.utcnow().isoformat()
#         last_activity = login_time
#         location_country = None
#         location_city = None
#         latitude = None
#         longitude = None
#         auth_method = 'otp'
#         # Assuming SessionModel is available via a chatbot backend API
#         # For now, we'll simulate a successful session creation
#         # In a real scenario, you'd call a chatbot backend endpoint
#         print("Simulating session creation for admin user...")
#         AuditLogModel().log(admin_email=email, action='admin_session_created', ip_address=ip_address, metadata={"session_id": session_id})
#     except Exception as e:
#         print("Failed to create admin session record", e)
#     return {"success": True, "token": token, "role": "admin"}

# NEW: 2FA-based authentication endpoints
@app.post('/admin/request-2fa-setup')
@log_action('2fa_setup_request')
async def admin_request_2fa_setup(data: OTPRequest, request: Request):
    """Request 2FA setup for admin user - generates QR code and secret"""
    email = data.email
    user = AdminUserModel().get_admin_by_email(email)
    if not user or not user.get('active', 1):
        ip = request.client.host if request else None
        user_agent = request.headers.get('user-agent') if request else None
        FailedLoginModel().log(email, ip, user_agent, reason='not in admin db or inactive')
        raise HTTPException(status_code=403, detail='Unauthorized')
    
    # Check if user already has 2FA set up
    if user.get('two_fa_secret_admin'):
        raise HTTPException(status_code=400, detail='2FA already set up for this admin')
    
    # Generate new 2FA secret
    secret = pyotp.random_base32()
    
    # Update user with new secret
    with AdminUserModel()._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('UPDATE admin_users SET two_fa_secret_admin = ? WHERE email = ?', (secret, email))
        conn.commit()
    
    # Generate QR code URL and convert to base64 image
    qr_url = AdminTOTP(secret).get_qr_url(email)
    
    # Generate actual QR code image as base64
    import qrcode
    import base64
    from io import BytesIO
    
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(qr_url)
    qr.make(fit=True)
    
    qr_image = qr.make_image(fill_color="black", back_color="white")
    buffered = BytesIO()
    qr_image.save(buffered, format="PNG")
    qr_base64 = base64.b64encode(buffered.getvalue()).decode()
    qr_data_url = f"data:image/png;base64,{qr_base64}"
    
    return {"success": True, "qr_url": qr_data_url, "secret": secret, "message": "2FA setup initiated"}

@app.post('/admin/verify-2fa')
@log_action('2fa_login')
async def admin_verify_2fa(data: TOTPVerify, request: Request):
    """Verify 2FA code and authenticate admin user"""
    email = data.email
    totp_code = data.totp
    
    user = AdminUserModel().get_admin_by_email(email)
    if not user or not user.get('active', 1):
        raise HTTPException(status_code=403, detail='Unauthorized')
    
    secret = user.get('two_fa_secret_admin')
    if not secret:
        raise HTTPException(status_code=400, detail='2FA not set up for this admin. Please set up 2FA first.')
    
    totp_service = AdminTOTP(secret)
    if not totp_service.verify_code(totp_code):
        raise HTTPException(status_code=401, detail='Invalid 2FA code')
    
    # Log device information
    user_agent = request.headers.get('user-agent', 'unknown')
    ip = request.client.host
    device_info = f"{user_agent}|{ip}"
    
    with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''SELECT id FROM admin_devices WHERE email = ? AND device_info = ?''', (email, device_info))
        if not cursor.fetchone():
            cursor.execute('''INSERT INTO admin_devices (email, device_info, first_seen) VALUES (?, ?, ?)''', (email, device_info, datetime.utcnow().isoformat()))
        conn.commit()
        # Notify about new device (optional)
        try:
            email_service = EmailService()
            email_service.send_email([email], 'New Admin Device Login', f'New device/browser detected:\n{user_agent}\nIP: {ip}\nTime: {datetime.utcnow().isoformat()}')
        except Exception as e:
            print(f"Failed to send new device notification: {e}")
    
    # Issue JWT token
    token = create_jwt(user)
    
    # Create session record
    try:
        import uuid
        session_id = str(uuid.uuid4())
        ip_address = request.client.host if request else None
        user_agent = request.headers.get('user-agent') if request else None
        login_time = datetime.utcnow().isoformat()
        last_activity = login_time
        auth_method = '2fa'
        
        print("Creating admin session record for 2FA login...")
        AuditLogModel().log(admin_email=email, action='admin_session_created', ip_address=ip_address, metadata={"session_id": session_id, "auth_method": auth_method})
    except Exception as e:
        print("Failed to create admin session record", e)
    
    return {"success": True, "token": token, "role": "admin", "message": "2FA verification successful"}

@app.post('/admin/check-2fa-status')
async def admin_check_2fa_status(data: OTPRequest, request: Request):
    """Check if admin user has 2FA set up"""
    email = data.email
    user = AdminUserModel().get_admin_by_email(email)
    if not user or not user.get('active', 1):
        raise HTTPException(status_code=403, detail='Unauthorized')
    
    has_2fa = bool(user.get('two_fa_secret_admin'))
    
    return {"success": True, "has_2fa": has_2fa, "email": email}

# Keep existing TOTP verification endpoint for backward compatibility
@app.post('/admin/verify-totp')
@log_action('totp_setup')
async def admin_verify_totp(data: TOTPVerify, request: Request, payload=Depends(rbac_required(['admin']))):
    email = data.email
    totp_code = data.totp
    user = AdminUserModel().get_admin_by_email(email)
    if not user or not user.get('active', 1):
        raise HTTPException(status_code=403, detail='Unauthorized')
    secret = user.get('two_fa_secret_admin')
    if not secret:
        raise HTTPException(status_code=400, detail='TOTP not set up for this admin')
    totp_service = AdminTOTP(secret)
    if not totp_service.verify_code(totp_code):
        raise HTTPException(status_code=401, detail='Invalid TOTP code')
    user_agent = request.headers.get('user-agent', 'unknown')
    ip = request.client.host
    device_info = f"{user_agent}|{ip}"
    with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''SELECT id FROM admin_devices WHERE email = ? AND device_info = ?''', (email, device_info))
        if not cursor.fetchone():
            cursor.execute('''INSERT INTO admin_devices (email, device_info, first_seen) VALUES (?, ?, ?)''', (email, device_info, datetime.utcnow().isoformat()))
            conn.commit()
            email_service = EmailService()
            email_service.send_email([email], 'New Admin Device Login', f'New device/browser detected:\n{user_agent}\nIP: {ip}\nTime: {datetime.utcnow().isoformat()}')
    # Issue final JWT
    token = create_jwt(user)
    # --- Create session record for admin user ---
    try:
        import uuid
        session_id = str(uuid.uuid4())
        ip_address = request.client.host if request else None
        user_agent = request.headers.get('user-agent') if request else None
        browser = None
        os = None
        device_fingerprint = None
        login_time = datetime.utcnow().isoformat()
        last_activity = login_time
        location_country = None
        location_city = None
        latitude = None
        longitude = None
        auth_method = 'totp'
        # Assuming SessionModel is available via a chatbot backend API
        # For now, we'll simulate a successful session creation
        # In a real scenario, you'd call a chatbot backend endpoint
        print("Simulating session creation for admin user...")
        AuditLogModel().log(admin_email=email, action='admin_session_created', ip_address=ip_address, metadata={"session_id": session_id})
    except Exception as e:
        print("Failed to create admin session record", e)
    return {"success": True, "token": token, "role": "admin"}

@app.post('/admin/setup-totp')
@log_action('totp_setup')
async def admin_setup_totp(data: OTPRequest, request: Request):
    email = data.email
    admin_model = AdminUserModel()
    user = admin_model.get_admin_by_email(email)
    if not user or not user.get('active', 1):
        raise HTTPException(status_code=403, detail='Unauthorized')
    secret = pyotp.random_base32()
    with admin_model._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('UPDATE admin_users SET two_fa_secret = ? WHERE email = ?', (secret, email))
        conn.commit()
    qr_url = AdminTOTP(secret).get_qr_url(email)
    return {"success": True, "qr_url": qr_url, "secret": secret}

@app.post("/api/admin/register")
async def admin_register(data: AdminRegisterRequest):
    admin_model = AdminUserModel()
    result = admin_model.create_admin(
        email=data.email,
        full_name=data.full_name
        # password=data.password,  # Extend create_admin to store password if needed
    )
    if result.get("success"):
        return {"success": True, "admin_id": result.get("admin_id")}
    else:
        raise HTTPException(status_code=400, detail=result.get("message", "Registration failed"))

# Read-only analytics endpoint: total chatbot users
@app.get('/admin/stats/users', dependencies=[Depends(rbac_required(['admin', 'superadmin']))])
@log_action('view_stats_users')
def admin_stats_users(request: Request):
    try:
        # Assuming ChatbotUserModel is available via a chatbot backend API
        # For now, we'll simulate a successful count
        print("Simulating user count for admin stats...")
        response = requests.get('http://localhost:5051/api/user-metrics', headers={'Authorization': f'Bearer {create_jwt({"id": 1, "email": "<EMAIL>", "role": "admin"})}'})
        if response.status_code == 200:
            return {"total_users": response.json().get('total_users', 0)}
        else:
            return {"total_users": 0}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get('/admin/stats/usage', dependencies=[Depends(rbac_required(['admin', 'superadmin', 'hr_lead', 'viewer']))])
@log_action('view_stats_usage')
def admin_stats_usage(request: Request):
    try:
        response = requests.get('http://localhost:5051/api/user-metrics', headers={'Authorization': f'Bearer {create_jwt({"id": 1, "email": "<EMAIL>", "role": "admin"})}'})
        if response.status_code == 200:
            usage_data = response.json().get('usage', [])
            # Convert timestamp strings to datetime objects for sorting
            for item in usage_data:
                item['date'] = datetime.fromisoformat(item['date'])
            # Sort by date descending
            usage_data.sort(key=lambda x: x['date'], reverse=True)
            return {"usage": usage_data}
        else:
            return {"usage": []}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get('/admin/stats/intents', dependencies=[Depends(rbac_required(['admin', 'superadmin', 'hr_lead', 'viewer']))])
@log_action('view_stats_intents')
def admin_stats_intents(request: Request):
    try:
        response = requests.get('http://localhost:5051/api/user-metrics', headers={'Authorization': f'Bearer {create_jwt({"id": 1, "email": "<EMAIL>", "role": "admin"})}'})
        if response.status_code == 200:
            intents_data = response.json().get('intents', [])
            # Sort by count descending
            intents_data.sort(key=lambda x: x['count'], reverse=True)
            return {"intents": intents_data}
        else:
            return {"intents": []}
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})

class AuditLogModel:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()
    def _get_connection(self):
        return sqlite3.connect(self.db_path)
    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    admin_email TEXT NOT NULL,
                    action TEXT NOT NULL,
                    ip_address TEXT,
                    metadata TEXT
                )
            ''')
            conn.commit()
    def log(self, admin_email, action, ip_address=None, metadata=None):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO audit_logs (timestamp, admin_email, action, ip_address, metadata)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                datetime.utcnow().isoformat() + 'Z',
                admin_email,
                action,
                ip_address,
                json.dumps(metadata or {})
            ))
            conn.commit()

class DeviceLogModel:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()
    def _get_connection(self):
        return sqlite3.connect(self.db_path)
    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS device_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    user_agent TEXT,
                    ip_address TEXT,
                    first_seen TEXT NOT NULL,
                    last_seen TEXT NOT NULL,
                    country TEXT,
                    region TEXT,
                    city TEXT
                )
            ''')
            # Migration: add geo columns if missing
            cursor.execute("PRAGMA table_info(device_log)")
            columns = [row[1] for row in cursor.fetchall()]
            if 'country' not in columns:
                cursor.execute('ALTER TABLE device_log ADD COLUMN country TEXT')
            if 'region' not in columns:
                cursor.execute('ALTER TABLE device_log ADD COLUMN region TEXT')
            if 'city' not in columns:
                cursor.execute('ALTER TABLE device_log ADD COLUMN city TEXT')
            conn.commit()
    async def log_device(self, email, user_agent, ip_address, country=None, region=None, city=None):
        now = datetime.utcnow().isoformat() + 'Z'
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''SELECT id FROM device_log WHERE email = ? AND user_agent = ? AND ip_address = ?''', (email, user_agent, ip_address))
            row = cursor.fetchone()
            if row:
                cursor.execute('''UPDATE device_log SET last_seen = ?, country = ?, region = ?, city = ? WHERE id = ?''', (now, country, region, city, row[0]))
            else:
                cursor.execute('''INSERT INTO device_log (email, user_agent, ip_address, first_seen, last_seen, country, region, city) VALUES (?, ?, ?, ?, ?, ?, ?, ?)''', (email, user_agent, ip_address, now, now, country, region, city))
            conn.commit()

class RevokedTokenModel:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()
    def _get_connection(self):
        return sqlite3.connect(self.db_path)
    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS revoked_tokens (
                    token_hash TEXT PRIMARY KEY,
                    email TEXT,
                    revoked_at TEXT,
                    expires_at TEXT
                )
            ''')
            conn.commit()
    def revoke(self, token, email, expires_at):
        import hashlib
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''INSERT OR IGNORE INTO revoked_tokens (token_hash, email, revoked_at, expires_at) VALUES (?, ?, ?, ?)''', (
                token_hash, email, datetime.utcnow().isoformat() + 'Z', expires_at))
            conn.commit()
    def is_revoked(self, token):
        import hashlib
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT 1 FROM revoked_tokens WHERE token_hash = ?', (token_hash,))
            return cursor.fetchone() is not None
    def cleanup(self):
        now = datetime.utcnow().isoformat() + 'Z'
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM revoked_tokens WHERE expires_at < ?', (now,))
            conn.commit()

class FailedLoginModel:
    def __init__(self):
        self.db_path = ADMIN_DB_PATH
        self._ensure_table()
    def _get_connection(self):
        return sqlite3.connect(self.db_path)
    def _ensure_table(self):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS failed_logins (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    attempted_at TEXT NOT NULL,
                    reason TEXT
                )
            ''')
            conn.commit()
    def log(self, email, ip_address, user_agent, reason=None):
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO failed_logins (email, ip_address, user_agent, attempted_at, reason)
                VALUES (?, ?, ?, ?, ?)
            ''', (email, ip_address, user_agent, datetime.utcnow().isoformat() + 'Z', reason))
            conn.commit()

@app.post('/admin/logout')
@log_action('logout')
async def admin_logout(request: Request, payload=Depends(rbac_required(['admin', 'superadmin', 'hr_lead', 'viewer']))):
    auth = request.headers.get('authorization')
    if not auth or not auth.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Missing or invalid token')
    token = auth.split(' ')[1]
    jwt_payload = verify_jwt(token)
    if not jwt_payload:
        raise HTTPException(status_code=401, detail='Invalid token')
    email = jwt_payload.get('email')
    exp = jwt_payload.get('exp')
    expires_at = datetime.utcfromtimestamp(exp).isoformat() + 'Z' if exp else None
    RevokedTokenModel().revoke(token, email, expires_at)
    return {"success": True, "message": "Logged out and token revoked"}

# Add middleware to reject revoked tokens for all /admin/ routes
from fastapi import Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware

class JWTRevocationMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request, call_next):
        if request.url.path.startswith('/admin/'):
            auth = request.headers.get('authorization')
            if auth and auth.startswith('Bearer '):
                token = auth.split(' ')[1]
                if RevokedTokenModel().is_revoked(token):
                    return Response(content='{"detail": "Token revoked"}', status_code=401, media_type='application/json')
        return await call_next(request)

app.add_middleware(JWTRevocationMiddleware)

@app.get('/admin/super/logs', dependencies=[Depends(rbac_required(['superadmin']))])
@log_action('superadmin_view_logs')
async def superadmin_view_logs(request: Request):
    logs = []
    with AuditLogModel()._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM audit_logs ORDER BY timestamp DESC LIMIT 1000')
        for row in cursor.fetchall():
            logs.append(dict(zip([d[0] for d in cursor.description], row)))
    return {"logs": logs}

@app.get('/admin/super/devices', dependencies=[Depends(rbac_required(['superadmin']))])
@log_action('superadmin_view_devices')
async def superadmin_view_devices(request: Request):
    devices = []
    with DeviceLogModel()._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM device_log ORDER BY last_seen DESC LIMIT 1000')
        for row in cursor.fetchall():
            devices.append(dict(zip([d[0] for d in cursor.description], row)))
    return {"devices": devices}

@app.post('/admin/super/revoke-session', dependencies=[Depends(rbac_required(['superadmin']))])
@log_action('superadmin_revoke_session')
async def superadmin_revoke_session(request: Request, data: dict):
    token = data.get('token')
    email = data.get('email')
    exp = data.get('exp')
    if not token or not email or not exp:
        raise HTTPException(status_code=400, detail='token, email, exp required')
    RevokedTokenModel().revoke(token, email, exp)
    return {"success": True, "message": "Session revoked"}

@app.patch('/admin/super/change-role', dependencies=[Depends(rbac_required(['superadmin']))])
@log_action('superadmin_change_role')
async def superadmin_change_role(request: Request, data: dict):
    email = data.get('email')
    new_role = data.get('role')
    if not email or not new_role:
        raise HTTPException(status_code=400, detail='email and role required')
    admin_model = AdminUserModel()
    with admin_model._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('UPDATE admin_users SET role = ? WHERE email = ?', (new_role, email))
        conn.commit()
        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail='Admin not found')
    return {"success": True, "message": f"Role updated to {new_role}"}

# Central role-to-permissions mapping
ROLE_PERMISSIONS = {
    'SUPERADMIN': [
        'manage_users', 'view_analytics', 'edit_settings', 'view_audit_logs', 'user:*', 'system:*', 'audit:*', 'metrics:*', 'chat:*', 'feedback:*', 'integration:*', 'export:*'
    ],
    'ADMIN': [
        'manage_users', 'view_analytics', 'user:read', 'user:create', 'user:update', 'audit:read', 'metrics:read', 'chat:read', 'chat:escalate', 'feedback:read', 'feedback:create', 'export:read'
    ],
    'VIEWER': [
        'view_analytics', 'metrics:read', 'chat:read', 'feedback:read'
    ],
    # Add other roles as needed
}

@app.get('/api/admin-users/me')
async def get_current_admin_user(authorization: str = Header(None)):
    print(f"[DEBUG] /api/admin-users/me called. Authorization header: {authorization}")
    if not authorization or not authorization.startswith('Bearer '):
        print("[DEBUG] Missing or invalid Authorization header.")
        raise HTTPException(status_code=401, detail='Missing or invalid token')
    token = authorization.split(' ')[1]
    payload = verify_jwt(token)
    print(f"[DEBUG] Decoded JWT payload: {payload}")
    if not payload:
        print("[DEBUG] Invalid token. Could not decode JWT.")
        raise HTTPException(status_code=401, detail='Invalid token')
    email = payload.get('email')
    user = AdminUserModel().get_admin_by_email(email)
    print(f"[DEBUG] DB user lookup for email {email}: {user}")
    if not user:
        print("[DEBUG] Admin user not found in DB.")
        raise HTTPException(status_code=404, detail='Admin user not found')
    role = user.get('role', 'admin').upper()
    role_level = 3 if role == 'SUPERADMIN' else 2 if role == 'ADMIN' else 1
    permissions = ROLE_PERMISSIONS.get(role, ['view_analytics'])
    return {
        'id': user.get('id'),
        'email': user.get('email'),
        'role': role,
        'role_level': role_level,
        'permissions': permissions,
        'full_name': user.get('full_name'),
        'active': user.get('active', 1),
        'tenant_id': user.get('tenant_id')
    }

# Change /admin-users to /api/admin-users
@app.get('/api/admin-users')
def get_all_admin_users():
    admin_model = AdminUserModel()
    users = admin_model.get_admin_users()
    if not users:
        return JSONResponse(status_code=404, content={"detail": "No admin users found"})
    return {"users": users}

@app.post("/api/admin-users")
async def create_admin_user(data: AdminUserCreateRequest):
    admin_model = AdminUserModel()
    # Check if user already exists
    if admin_model.get_admin_by_email(data.email):
        raise HTTPException(status_code=400, detail="Admin already exists")
    role = data.role.upper() if data.role else None
    result = admin_model.create_admin(
        email=data.email,
        full_name=data.full_name,
        role=role,
        tenant_id=data.tenant_id
    )
    if result.get("success"):
        return {"success": True, "admin_id": result.get("admin_id")}
    else:
        raise HTTPException(status_code=400, detail=result.get("message", "Failed to create admin user"))

@app.put("/api/admin-users/{user_id}")
async def update_admin_user(user_id: int = Path(...), data: AdminUserUpdateRequest = None):
    admin_model = AdminUserModel()
    # Only update provided fields
    role = data.role.upper() if data.role else None
    updated = admin_model.update_user(
        user_id=user_id,
        full_name=data.full_name,
        role=role,
        tenant_id=data.tenant_id
    )
    if updated:
        return {"success": True, "message": "Admin user updated"}
    else:
        raise HTTPException(status_code=404, detail="Admin user not found or nothing to update")

@app.delete("/api/admin-users/{user_id}")
def delete_admin_user(user_id: int):
    admin_model = AdminUserModel()
    with admin_model._get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM admin_users WHERE id = ?', (user_id,))
        conn.commit()
        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Admin user not found")
    return {"success": True, "message": "Admin user deleted"}

# Stub: /feedback/trends
@app.get('/feedback/trends')
def feedback_trends(range: str = "7d"):
    return {"trends": []}

# Stub: /ai/weekly-digest
@app.get('/ai/weekly-digest')
def ai_weekly_digest():
    return {"digest": []}

# Stub: /escalations/pending
@app.get('/escalations/pending')
def escalations_pending():
    import json
    with sqlite3.connect(ESCALATION_DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM escalations ORDER BY created_at DESC')
        rows = cursor.fetchall()
        columns = [d[0] for d in cursor.description]
        pending = []
        for row in rows:
            rec = dict(zip(columns, row))
            user_email = None
            try:
                if rec['user_json']:
                    user_info = json.loads(rec['user_json'])
                    user_email = user_info.get('email')
            except Exception:
                pass
            pending.append({
                'id': rec['id'],
                'user': user_email or 'N/A',
                'timestamp': rec['created_at'],
                'topic': rec['issue_type'],
            })
    return {'pending': pending}

# Stub: /compliance/gdpr-logs
@app.get('/compliance/gdpr-logs')
def compliance_gdpr_logs(range: str = "7d", page: int = 1, pageSize: int = 10):
    return {"logs": [], "page": page, "pageSize": pageSize, "total": 0}

# Stub: /api/sessions/active
@app.get('/api/sessions/active')
def api_sessions_active(user_type: str = "admin"):
    session_model = SessionModel()
    sessions = session_model.get_active_sessions(user_type=user_type)
    return {"sessions": sessions, "user_type": user_type}

@app.get('/api/sessions/history')
def api_sessions_history(
    user_type: str = Query("admin"),
    range: str = Query("7d"),
    user_id: str = Query(None),
    ip_address: str = Query(None),
    auth_method: str = Query(None),
    success: str = Query(None),
    page: int = Query(1),
    pageSize: int = Query(10)
):
    session_model = SessionModel()
    filters = {}
    if user_id:
        filters["user_id"] = user_id
    if ip_address:
        filters["ip_address"] = ip_address
    if auth_method:
        filters["auth_method"] = auth_method
    if success is not None:
        filters["success"] = int(success)
    offset = (page - 1) * pageSize
    # For now, ignore range filtering for simplicity
    logs = session_model.get_session_history(user_type=user_type, limit=pageSize, offset=offset, filters=filters)
    total = len(logs)  # For real use, implement a count query
    return {"logs": logs, "total": total, "page": page, "pageSize": pageSize}

@app.get('/api/roles')
def get_roles():
    # Example static roles; adjust as needed
    roles = [
        {"id": 1, "name": "superadmin", "level": 3},
        {"id": 2, "name": "admin", "level": 2},
        {"id": 3, "name": "viewer", "level": 1}
    ]
    return roles

# Load admin dashboard specific environment
from dotenv import load_dotenv
load_dotenv('.env.admin')

CHATBOT_BACKEND_URL = os.getenv('CHATBOT_BACKEND_URL', 'http://localhost:5051')
CHATBOT_API_VERSION = os.getenv('CHATBOT_API_VERSION', 'v1')
CHATBOT_API_TIMEOUT = int(os.getenv('CHATBOT_API_TIMEOUT', '30'))

api_router = APIRouter()

@api_router.get('/api/chat-analytics/live')
def proxy_chat_analytics_live():
    try:
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/chat-analytics/live', timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/chatlogs')
def proxy_chatlogs():
    try:
        # Forward query parameters
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/chatlogs',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/metrics/{metric_name}')
def proxy_metrics(metric_name: str):
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/metrics/{metric_name}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

# ============================================================================
# NEW V1 API PROXY ENDPOINTS FOR ADMIN DASHBOARD INTEGRATION
# ============================================================================

@api_router.get('/api/analytics/{path:path}')
def proxy_analytics(path: str):
    """Proxy all analytics endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/analytics/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/training/{path:path}')
def proxy_training(path: str):
    """Proxy all training endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/training/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/compliance/{path:path}')
def proxy_compliance(path: str):
    """Proxy all compliance endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/compliance/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.post('/api/compliance/{path:path}')
def proxy_compliance_post(path: str):
    """Proxy POST requests to compliance endpoints."""
    try:
        json_data = request.json if hasattr(request, 'json') else {}
        resp = requests.post(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/compliance/{path}',
                           json=json_data, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/sessions/{path:path}')
def proxy_sessions(path: str):
    """Proxy all sessions endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/sessions/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/escalations/{path:path}')
def proxy_escalations(path: str):
    """Proxy escalations endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/escalations/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

@api_router.get('/api/feedback/{path:path}')
def proxy_feedback(path: str):
    """Proxy feedback endpoints to chatbot backend."""
    try:
        query_params = dict(request.query_params)
        resp = requests.get(f'{CHATBOT_BACKEND_URL}/api/{CHATBOT_API_VERSION}/feedback/{path}',
                          params=query_params, timeout=CHATBOT_API_TIMEOUT)
        try:
            data = resp.json()
            return JSONResponse(content=data, status_code=resp.status_code)
        except Exception:
            return JSONResponse(content={"error": "Chatbot backend did not return valid JSON", "raw": resp.text}, status_code=502)
    except Exception as e:
        return JSONResponse(content={"error": f"Chatbot backend unreachable: {str(e)}"}, status_code=502)

app.include_router(api_router) 

# Escalation DB setup
ESCALATION_DB_PATH = 'admin_dashboard/data/escalations.db'

def ensure_escalation_table():
    import os
    # Ensure the directory exists
    db_dir = os.path.dirname(ESCALATION_DB_PATH)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir, exist_ok=True)
        print(f"Created directory: {db_dir}")
        
    with sqlite3.connect(ESCALATION_DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS escalations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                hr_person TEXT,
                issue_type TEXT,
                issue_description TEXT,
                priority TEXT,
                is_anonymous INTEGER,
                user_json TEXT,
                sender_name TEXT,
                sender_email TEXT,
                sender_employee_id TEXT,
                created_at TIMESTAMP NOT NULL,
                assigned_to TEXT,
                assigned_at TIMESTAMP,
                status TEXT DEFAULT 'pending',
                hr_reply TEXT,
                hr_reply_at TIMESTAMP,
                employee_reply TEXT,
                employee_reply_at TIMESTAMP,
                assigned_admin TEXT,
                due_date TIMESTAMP,
                routing_audit_log TEXT
            )
        ''')
        
        # Add new columns to existing table if they don't exist
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN sender_name TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN sender_email TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN sender_employee_id TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN assigned_admin TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN due_date TIMESTAMP")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        try:
            cursor.execute("ALTER TABLE escalations ADD COLUMN routing_audit_log TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists
            
        conn.commit()
        print(f"Ensured escalation table exists in: {ESCALATION_DB_PATH}")
ensure_escalation_table()

# Remove EscalationRequest model and /api/escalation/submit endpoint
# Remove lines defining EscalationRequest and submit_escalation

@app.get('/api/escalations')
async def get_user_escalations(authorization: str = Header(None)):
    # Get user email from JWT
    email = None
    role = None
    if authorization and authorization.startswith('Bearer '):
        token = authorization.split(' ')[1]
        payload = verify_jwt(token)
        if payload:
            email = payload.get('email')
            role = payload.get('role', '').upper()
    if not email and role != 'SUPERADMIN':
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)
    
    with sqlite3.connect(ESCALATION_DB_PATH) as conn:
        cursor = conn.cursor()
        if role == 'SUPERADMIN':
            # Superadmin sees all escalations
            cursor.execute('SELECT * FROM escalations ORDER BY created_at DESC')
        elif role in ['HR_LEAD', 'HR_ADMIN']:
            # HR leads see escalations assigned to them or unassigned ones
            cursor.execute('''
                SELECT * FROM escalations 
                WHERE assigned_to = ? OR assigned_to IS NULL 
                ORDER BY created_at DESC
            ''', (email,))
        else:
            # Regular users see only their own escalations
            cursor.execute('SELECT * FROM escalations ORDER BY created_at DESC')
        
        rows = cursor.fetchall()
        columns = [d[0] for d in cursor.description]
        result = []
        for row in rows:
            rec = dict(zip(columns, row))
            # Parse user_json
            user_email = None
            try:
                import json
                if rec['user_json']:
                    user_info = json.loads(rec['user_json'])
                    user_email = user_info.get('email')
            except Exception:
                pass
            
            # Filter based on role and assignment
            if role == 'SUPERADMIN':
                result.append(rec)
            elif role in ['HR_LEAD', 'HR_ADMIN']:
                # HR sees assigned to them or unassigned
                if rec['assigned_to'] == email or rec['assigned_to'] is None:
                    result.append(rec)
            else:
                # Regular users see only their own escalations
                if user_email == email:
                    result.append(rec)
        
        return {"escalations": result}

@app.get('/api/escalations/{escalation_id}')
async def get_escalation_details(escalation_id: int, authorization: str = Header(None)):
    """Get specific escalation details"""
    email = None
    role = None
    if authorization and authorization.startswith('Bearer '):
        token = authorization.split(' ')[1]
        payload = verify_jwt(token)
        if payload:
            email = payload.get('email')
            role = payload.get('role', '').upper()
    
    if not email:
        return JSONResponse(content={"error": "Unauthorized"}, status_code=401)
    
    with sqlite3.connect(ESCALATION_DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM escalations WHERE id = ?', (escalation_id,))
        row = cursor.fetchone()
        
        if not row:
            return JSONResponse(content={"error": "Escalation not found"}, status_code=404)
        
        columns = [d[0] for d in cursor.description]
        escalation = dict(zip(columns, row))
        
        # Check permissions
        if role == 'SUPERADMIN':
            return {"escalation": escalation}
        elif role in ['HR_LEAD', 'HR_ADMIN']:
            if escalation['assigned_to'] == email or escalation['assigned_to'] is None:
                return {"escalation": escalation}
            else:
                return JSONResponse(content={"error": "Not assigned to you"}, status_code=403)
        else:
            # Regular user - check if it's their escalation
            try:
                user_info = json.loads(escalation['user_json'])
                user_email = user_info.get('email')
                if user_email == email:
                    return {"escalation": escalation}
                else:
                    return JSONResponse(content={"error": "Not your escalation"}, status_code=403)
            except:
                return JSONResponse(content={"error": "Invalid escalation data"}, status_code=400)

class NewEscalationRequest(BaseModel):
    user_id: str = Field(...)
    message: str = Field(...)
    issue_type: str = Field(...)
    timestamp: str = Field(...)
    priority: Optional[str] = None
    user: Optional[dict] = None
    uploadedFiles: Optional[List[str]] = None
    hrEmail: Optional[str] = None

# HR email mapping
hr_map = {
    'Vijay Kumar Kodam': '<EMAIL>',
    'Suman': '<EMAIL>',
    'Chandra Shekar': '<EMAIL>',
    'Lavanya': '<EMAIL>',
}

@app.post('/api/escalations')
async def create_escalation(request: Request, files: Optional[List[UploadFile]] = File(None)):
    import os
    import json
    import datetime
    import tempfile
    
    try:
        if request.headers.get('content-type', '').startswith('multipart/form-data'):
            form = await request.form()
            data = {k: form[k] for k in form if k != 'files'}
            for k in ['user', 'uploadedFiles']:
                if k in data and isinstance(data[k], str):
                    try:
                        data[k] = json.loads(data[k])
                    except Exception:
                        pass
            # Build email body with only present and non-empty fields
            body_lines = ["New HR Escalation Submitted\n"]
            if data.get('issue_type'):
                body_lines.append(f"Issue Type: {data['issue_type']}")
            if data.get('priority'):
                body_lines.append(f"Priority: {data['priority']}")
            if data.get('timestamp'):
                body_lines.append(f"Timestamp: {data['timestamp']}")
            user_info = data.get('user')
            if user_info:
                user_section = []
                if user_info.get('fullName') or user_info.get('full_name'):
                    user_section.append(f"Name: {user_info.get('fullName') or user_info.get('full_name')}")
                if user_info.get('email'):
                    user_section.append(f"Email: {user_info.get('email')}")
                if user_info.get('employeeId') or user_info.get('employee_id'):
                    user_section.append(f"Employee ID: {user_info.get('employeeId') or user_info.get('employee_id')}")
                if user_section:
                    body_lines.append("\n".join(user_section))
            if data.get('message'):
                body_lines.append(f"\nIssue:\n{data['message']}")
            if files:
                file_names = [f.filename for f in files if f.filename]
                if file_names:
                    body_lines.append("\nUploaded Files:\n" + "\n".join(file_names))
            body = "\n".join(body_lines)
            hr_email = data.get('hrEmail') or '<EMAIL>'
            attachments = []
            if files:
                for f in files:
                    if f.filename:
                        temp = tempfile.NamedTemporaryFile(delete=False)
                        temp.write(await f.read())
                        temp.close()
                        attachments.append((f.filename, temp.name))
            try:
                result = EmailService().send_email([hr_email], f"[Escalation] {data.get('issue_type','')}", body, attachments=attachments)
                print("EmailService send_email result:", result)
                if not result.get("success"):
                    print("Email sending failed:", result.get("message"))
            except Exception as e:
                print({"event": "escalation_email_failed", "reason": str(e)})
            finally:
                for _, path in attachments:
                    os.unlink(path)
            # Save escalation to DB  
            hr_email = data.get('hrEmail')
            # Try to find HR person name from email using the hr_map
            hr_person = None
            if hr_email:
                for name, email in hr_map.items():
                    if email == hr_email:
                        hr_person = name
                        break
                if not hr_person:
                    hr_person = hr_email  # Fallback to email if name not found
            
            issue_type = data.get('issue_type')
            issue_description = data.get('message')
            priority = data.get('priority')
            is_anonymous = int(data.get('is_anonymous', 0))
            user_data = data.get('user', {})
            user_json = json.dumps(user_data)
            created_at = datetime.datetime.utcnow().isoformat()
            
            # Extract user information for dedicated columns
            sender_name = None
            sender_email = None
            sender_employee_id = None
            
            if not is_anonymous and user_data:
                sender_name = user_data.get('fullName', '')
                sender_email = user_data.get('email', '')
                sender_employee_id = user_data.get('employeeId', '')
            
            # Debug logging
            print(f"DEBUG: Multipart escalation data received:")
            print(f"  hrEmail: {data.get('hrEmail')}")
            print(f"  issue_type: {data.get('issue_type')}")
            print(f"  message: {data.get('message')}")
            print(f"  priority: {data.get('priority')}")
            print(f"  is_anonymous: {data.get('is_anonymous')}")
            print(f"  user: {data.get('user')}")
            print(f"  sender_name: {sender_name}")
            print(f"  sender_email: {sender_email}")
            print(f"  sender_employee_id: {sender_employee_id}")
            print(f"  All data keys: {list(data.keys())}")
            
            try:
                # Ensure the directory exists
                db_dir = os.path.dirname(ESCALATION_DB_PATH)
                if not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                    print(f"Created directory: {db_dir}")
                    
                with sqlite3.connect(ESCALATION_DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO escalations (hr_person, issue_type, issue_description, priority, is_anonymous, user_json, sender_name, sender_email, sender_employee_id, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (hr_person, issue_type, issue_description, priority, is_anonymous, user_json, sender_name, sender_email, sender_employee_id, created_at))
                    conn.commit()
                    print(f"Successfully saved escalation to database: {ESCALATION_DB_PATH}")
            except Exception as e:
                print(f"Database error: {e}")
                print(f"Database path: {ESCALATION_DB_PATH}")
                print(f"Current working directory: {os.getcwd()}")
                # Don't fail the request if DB save fails, email was already sent
                pass
                
            return JSONResponse({"success": True})
        else:
            data = await request.json()
            body_lines = ["New HR Escalation Submitted\n"]
            if data.get('issue_type'):
                body_lines.append(f"Issue Type: {data['issue_type']}")
            if data.get('priority'):
                body_lines.append(f"Priority: {data['priority']}")
            if data.get('timestamp'):
                body_lines.append(f"Timestamp: {data['timestamp']}")
            user_info = data.get('user')
            if user_info:
                user_section = []
                if user_info.get('fullName') or user_info.get('full_name'):
                    user_section.append(f"Name: {user_info.get('fullName') or user_info.get('full_name')}")
                if user_info.get('email'):
                    user_section.append(f"Email: {user_info.get('email')}")
                if user_info.get('employeeId') or user_info.get('employee_id'):
                    user_section.append(f"Employee ID: {user_info.get('employeeId') or user_info.get('employee_id')}")
                if user_section:
                    body_lines.append("\n".join(user_section))
            if data.get('message'):
                body_lines.append(f"\nIssue:\n{data['message']}")
            if data.get('uploadedFiles'):
                file_names = [f for f in data['uploadedFiles'] if f]
                if file_names:
                    body_lines.append("\nUploaded Files:\n" + "\n".join(file_names))
            body = "\n".join(body_lines)
            hr_email = data.get('hrEmail') or '<EMAIL>'
            try:
                result = EmailService().send_email([hr_email], f"[Escalation] {data.get('issue_type','')}", body)
                print("EmailService send_email result:", result)
                if not result.get("success"):
                    print("Email sending failed:", result.get("message"))
            except Exception as e:
                print({"event": "escalation_email_failed", "reason": str(e)})
            # Save escalation to DB  
            hr_email = data.get('hrEmail')
            # Try to find HR person name from email using the hr_map
            hr_person = None
            if hr_email:
                for name, email in hr_map.items():
                    if email == hr_email:
                        hr_person = name
                        break
                if not hr_person:
                    hr_person = hr_email  # Fallback to email if name not found
            
            issue_type = data.get('issue_type')
            issue_description = data.get('message')
            priority = data.get('priority')
            is_anonymous = int(data.get('is_anonymous', 0))
            user_data = data.get('user', {})
            user_json = json.dumps(user_data)
            created_at = datetime.datetime.utcnow().isoformat()
            
            # Extract user information for dedicated columns
            sender_name = None
            sender_email = None
            sender_employee_id = None
            
            if not is_anonymous and user_data:
                sender_name = user_data.get('fullName', '')
                sender_email = user_data.get('email', '')
                sender_employee_id = user_data.get('employeeId', '')
            
            # Debug logging
            print(f"DEBUG: JSON escalation data received:")
            print(f"  hrEmail: {data.get('hrEmail')}")
            print(f"  issue_type: {data.get('issue_type')}")
            print(f"  message: {data.get('message')}")
            print(f"  priority: {data.get('priority')}")
            print(f"  is_anonymous: {data.get('is_anonymous')}")
            print(f"  user: {data.get('user')}")
            print(f"  sender_name: {sender_name}")
            print(f"  sender_email: {sender_email}")
            print(f"  sender_employee_id: {sender_employee_id}")
            print(f"  All data keys: {list(data.keys())}")
            
            try:
                # Ensure the directory exists
                db_dir = os.path.dirname(ESCALATION_DB_PATH)
                if not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                    print(f"Created directory: {db_dir}")
                    
                with sqlite3.connect(ESCALATION_DB_PATH) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO escalations (hr_person, issue_type, issue_description, priority, is_anonymous, user_json, sender_name, sender_email, sender_employee_id, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (hr_person, issue_type, issue_description, priority, is_anonymous, user_json, sender_name, sender_email, sender_employee_id, created_at))
                    conn.commit()
                    print(f"Successfully saved escalation to database: {ESCALATION_DB_PATH}")
            except Exception as e:
                print(f"Database error: {e}")
                print(f"Database path: {ESCALATION_DB_PATH}")
                print(f"Current working directory: {os.getcwd()}")
                # Don't fail the request if DB save fails, email was already sent
                pass
                
            return JSONResponse({"success": True})
    except Exception as e:
        print(f"Unexpected error in create_escalation: {e}")
        import traceback
        traceback.print_exc()
        # Still return success since email was likely sent
        return JSONResponse({"success": True})

@app.patch('/api/escalations/{escalation_id}/assign')
async def assign_escalation(escalation_id: int, request: Request):
    """Assign escalation to HR team member"""
    try:
        data = await request.json()
        assigned_to = data.get('assigned_to')  # HR email
        assigned_by = data.get('assigned_by')  # Admin email
        
        with sqlite3.connect(ESCALATION_DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE escalations 
                SET assigned_to = ?, assigned_at = ?, status = 'assigned'
                WHERE id = ?
            ''', (assigned_to, datetime.datetime.utcnow().isoformat(), escalation_id))
            conn.commit()
            
        return JSONResponse({"success": True, "message": "Escalation assigned successfully"})
    except Exception as e:
        print(f"Error assigning escalation: {e}")
        return JSONResponse({"success": False, "message": "Failed to assign escalation"}, status_code=500)

@app.post('/api/escalations/{escalation_id}/route')
async def route_escalation(escalation_id: int, request: Request):
    """Route escalation to another admin if not within current admin's scope"""
    try:
        data = await request.json()
        target_admin_email = data.get('target_admin_email')
        reason = data.get('reason', '')
        due_date = data.get('due_date')  # ISO format string
        routed_by = data.get('routed_by')  # Current admin email
        
        if not target_admin_email:
            return JSONResponse({"success": False, "message": "Target admin email is required"}, status_code=400)
        
        # Get escalation details for notification
        with sqlite3.connect(ESCALATION_DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT sender_email, sender_name, issue_type, issue_description FROM escalations WHERE id = ?', (escalation_id,))
            escalation_data = cursor.fetchone()
            
            if not escalation_data:
                return JSONResponse({"success": False, "message": "Escalation not found"}, status_code=404)
            
            sender_email, sender_name, issue_type, issue_description = escalation_data
            
            # Update escalation with routing information
            current_time = datetime.datetime.utcnow().isoformat()
            
            # Create routing audit log entry
            routing_log = {
                "timestamp": current_time,
                "from_admin": routed_by,
                "to_admin": target_admin_email,
                "reason": reason,
                "action": "routed"
            }
            
            # Get existing audit log and append new entry
            cursor.execute('SELECT routing_audit_log FROM escalations WHERE id = ?', (escalation_id,))
            existing_log = cursor.fetchone()
            audit_log = []
            if existing_log and existing_log[0]:
                try:
                    import json
                    audit_log = json.loads(existing_log[0])
                except:
                    audit_log = []
            
            audit_log.append(routing_log)
            
            cursor.execute('''
                UPDATE escalations 
                SET assigned_admin = ?, due_date = ?, routing_audit_log = ?, status = 'routed', assigned_at = ?
                WHERE id = ?
            ''', (target_admin_email, due_date, json.dumps(audit_log), current_time, escalation_id))
            conn.commit()
        
        # Send notification to employee about reassignment
        try:
            if sender_email:
                notification_subject = f"Your HR Query Has Been Reassigned"
                notification_body = f"""
Dear {sender_name or 'Employee'},

Your HR query regarding "{issue_type}" has been reassigned to another HR representative for better assistance.

Issue: {issue_description}
Reassigned on: {current_time}
Due date: {due_date}

We apologize for any inconvenience and assure you that your query will be addressed promptly by the new representative.

Best regards,
HR Team
                """.strip()
                
                result = EmailService().send_email([sender_email], notification_subject, notification_body)
                if not result.get("success"):
                    print(f"Failed to send reassignment notification: {result.get('message')}")
        except Exception as e:
            print(f"Error sending reassignment notification: {e}")
        
        # Send notification to target admin
        try:
            admin_notification_subject = f"New HR Query Assigned to You"
            admin_notification_body = f"""
Hello,

You have been assigned a new HR query that requires your attention.

Query ID: {escalation_id}
Issue Type: {issue_type}
Issue: {issue_description}
Due Date: {due_date}
Assigned by: {routed_by}
Reason for routing: {reason}

Please review and respond to this query within the specified timeframe.

Best regards,
HR System
            """.strip()
            
            result = EmailService().send_email([target_admin_email], admin_notification_subject, admin_notification_body)
            if not result.get("success"):
                print(f"Failed to send admin notification: {result.get('message')}")
        except Exception as e:
            print(f"Error sending admin notification: {e}")
        
        return JSONResponse({"success": True, "message": "Escalation routed successfully"})
    except Exception as e:
        print(f"Error routing escalation: {e}")
        return JSONResponse({"success": False, "message": f"Failed to route escalation: {str(e)}"}, status_code=500)

@app.post('/api/escalations/{escalation_id}/reply')
async def reply_to_escalation(escalation_id: int, request: Request):
    """HR team member replies to escalation"""
    try:
        data = await request.json()
        reply_text = data.get('reply')
        replied_by = data.get('replied_by')  # HR email
        reply_type = data.get('reply_type', 'hr')  # 'hr' or 'employee'
        
        with sqlite3.connect(ESCALATION_DB_PATH) as conn:
            cursor = conn.cursor()
            if reply_type == 'hr':
                cursor.execute('''
                    UPDATE escalations 
                    SET hr_reply = ?, hr_reply_at = ?, status = 'replied'
                    WHERE id = ?
                ''', (reply_text, datetime.datetime.utcnow().isoformat(), escalation_id))
                
                # Get escalation details for email notification
                cursor.execute('SELECT user_json, issue_type FROM escalations WHERE id = ?', (escalation_id,))
                escalation_data = cursor.fetchone()
                
                if escalation_data and escalation_data[0]:  # user_json exists
                    try:
                        user_info = json.loads(escalation_data[0])
                        employee_email = user_info.get('email')
                        
                        if employee_email:
                            # Send email notification to employee
                            email_subject = f"HR Response to Your Escalation - {escalation_data[1]}"
                            email_body = f"""
Dear {user_info.get('fullName', 'Employee')},

You have received a response from HR regarding your escalation:

**Issue Type:** {escalation_data[1]}
**HR Response:** {reply_text}

Please log into your chatbot to view the full details and respond if needed.

Best regards,
HR Team
                            """
                            
                            try:
                                EmailService().send_email([employee_email], email_subject, email_body)
                                print(f"Email notification sent to {employee_email}")
                            except Exception as e:
                                print(f"Failed to send email notification: {e}")
                    except Exception as e:
                        print(f"Error processing user data for email: {e}")
                        
            else:
                cursor.execute('''
                    UPDATE escalations 
                    SET employee_reply = ?, employee_reply_at = ?, status = 'employee_replied'
                    WHERE id = ?
                ''', (reply_text, datetime.datetime.utcnow().isoformat(), escalation_id))
            conn.commit()
            
        return JSONResponse({"success": True, "message": "Reply sent successfully"})
    except Exception as e:
        print(f"Error replying to escalation: {e}")
        return JSONResponse({"success": False, "message": "Failed to send reply"}, status_code=500)

@app.get("/api/user/profile")
async def get_user_profile(authorization: str = Header(None)):
    if not authorization or not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Missing or invalid token')
    token = authorization.split(' ')[1]
    payload = verify_jwt(token)
    if not payload:
        raise HTTPException(status_code=401, detail='Invalid token')
    email = payload.get('email')
    user = AdminUserModel().get_admin_by_email(email)
    if not user:
        raise HTTPException(status_code=404, detail='User not found')
    return {
        "email": user.get("email"),
        "employee_id": user.get("employee_id"),
        "full_name": user.get("full_name"),
        "id": user.get("id")
    } 

@app.get('/api/admin-users/available-for-routing')
async def get_available_admins_for_routing(authorization: str = Header(None)):
    """Get list of available admin users for routing escalations"""
    try:
        # Get user email from JWT
        email = None
        role = None
        if authorization and authorization.startswith('Bearer '):
            token = authorization.split(' ')[1]
            payload = verify_jwt(token)
            if payload:
                email = payload.get('email')
                role = payload.get('role', '').upper()
        
        if not email or role not in ['SUPERADMIN', 'HR_LEAD', 'HR_ADMIN']:
            return JSONResponse(content={"error": "Unauthorized"}, status_code=401)
        
        # Get available admin users
        with sqlite3.connect('admin_dashboard/data/overrides.db') as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT email, full_name, role, active 
                FROM admin_users 
                WHERE active = 1 AND role IN ('HR_LEAD', 'HR_ADMIN', 'ADMIN')
                ORDER BY role, full_name
            ''')
            rows = cursor.fetchall()
            
            available_admins = []
            for row in rows:
                admin_email, full_name, admin_role, active = row
                # Don't include the current user
                if admin_email != email:
                    available_admins.append({
                        "email": admin_email,
                        "full_name": full_name or admin_email,
                        "role": admin_role,
                        "active": bool(active)
                    })
            
            return {"available_admins": available_admins}
    except Exception as e:
        print(f"Error getting available admins: {e}")
        return JSONResponse({"error": f"Failed to get available admins: {str(e)}"}, status_code=500)

@app.get('/api/escalations/{escalation_id}/routing-history')
async def get_routing_history(escalation_id: int, authorization: str = Header(None)):
    """Get routing history for a specific escalation"""
    try:
        # Get user email from JWT
        email = None
        role = None
        if authorization and authorization.startswith('Bearer '):
            token = authorization.split(' ')[1]
            payload = verify_jwt(token)
            if payload:
                email = payload.get('email')
                role = payload.get('role', '').upper()
        
        if not email or role not in ['SUPERADMIN', 'HR_LEAD', 'HR_ADMIN']:
            return JSONResponse(content={"error": "Unauthorized"}, status_code=401)
        
        # Get escalation routing history
        with sqlite3.connect(ESCALATION_DB_PATH) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT routing_audit_log FROM escalations WHERE id = ?', (escalation_id,))
            row = cursor.fetchone()
            
            if not row:
                return JSONResponse({"error": "Escalation not found"}, status_code=404)
            
            routing_log = row[0]
            if not routing_log:
                return {"routing_history": []}
            
            try:
                import json
                history = json.loads(routing_log)
                return {"routing_history": history}
            except:
                return {"routing_history": []}
    except Exception as e:
        print(f"Error getting routing history: {e}")
        return JSONResponse({"error": f"Failed to get routing history: {str(e)}"}, status_code=500)

# Start the escalation reminder manager
try:
    from .utils.async_task_manager import start_reminder_manager
    start_reminder_manager(ESCALATION_DB_PATH)
    print("Escalation reminder manager started successfully")
except Exception as e:
    print(f"Failed to start escalation reminder manager: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)