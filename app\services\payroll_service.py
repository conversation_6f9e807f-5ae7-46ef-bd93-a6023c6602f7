"""
Payroll Query Handling Service for HR Assistant Chatbot.
Handles personal payroll queries like salary discrepancies, reimbursement status, and bonuses.
Designed for easy future integration with HRMS or payroll APIs.
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import re

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PayrollQuery:
    """Represents a payroll-related query with extracted entities."""
    employee_id: str
    query_type: str  # 'salary', 'reimbursement', 'bonus', 'deduction', 'tax'
    month: Optional[str] = None
    year: Optional[str] = None
    amount: Optional[float] = None
    reimbursement_type: Optional[str] = None  # 'travel', 'medical', 'food', etc.
    description: str = ""
    confidence: float = 0.0


@dataclass
class PayrollResponse:
    """Represents a response to a payroll query."""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    escalated: bool = False
    escalation_reason: Optional[str] = None
    response_time: float = 0.0


class PayrollService:
    """
    Modular payroll query handling service.
    Designed for easy integration with external HRMS/payroll APIs.
    """
    
    def __init__(self):
        """Initialize the payroll service."""
        self.api_enabled = False  # Will be True when integrated with real API
        self.escalation_threshold = 0.7  # Confidence threshold for auto-handling
        logger.info("PayrollService initialized")
    
    def extract_payroll_entities(self, query: str, employee_id: str) -> PayrollQuery:
        """
        Extract payroll-related entities from the query.
        
        Args:
            query: User query text
            employee_id: Employee ID from session/auth
            
        Returns:
            PayrollQuery object with extracted entities
        """
        start_time = time.time()
        query_lower = query.lower()
        
        # Determine query type
        query_type = self._classify_payroll_query_type(query_lower)
        
        # Extract temporal entities
        month, year = self._extract_temporal_entities(query_lower)
        
        # Extract amount if mentioned
        amount = self._extract_amount(query_lower)
        
        # Extract reimbursement type
        reimbursement_type = self._extract_reimbursement_type(query_lower)
        
        # Calculate confidence based on entity extraction success
        confidence = self._calculate_extraction_confidence(
            query_type, month, year, amount, reimbursement_type
        )
        
        processing_time = time.time() - start_time
        
        payroll_query = PayrollQuery(
            employee_id=employee_id,
            query_type=query_type,
            month=month,
            year=year,
            amount=amount,
            reimbursement_type=reimbursement_type,
            description=query,
            confidence=confidence
        )
        
        logger.info(f"Extracted payroll entities: type={query_type}, month={month}, "
                   f"year={year}, amount={amount}, confidence={confidence:.2f}, "
                   f"time={processing_time:.3f}s")
        
        return payroll_query
    
    def handle_payroll_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """
        Handle a payroll query and return appropriate response.
        
        Args:
            payroll_query: PayrollQuery object with extracted entities
            
        Returns:
            PayrollResponse with result or escalation info
        """
        start_time = time.time()
        
        try:
            # Check if we have enough confidence to auto-handle
            if payroll_query.confidence < self.escalation_threshold:
                return self._escalate_to_hr(
                    payroll_query, 
                    f"Low confidence in entity extraction ({payroll_query.confidence:.2f})"
                )
            
            # Route to specific handler based on query type
            if payroll_query.query_type == 'salary':
                response = self._handle_salary_query(payroll_query)
            elif payroll_query.query_type == 'reimbursement':
                response = self._handle_reimbursement_query(payroll_query)
            elif payroll_query.query_type == 'bonus':
                response = self._handle_bonus_query(payroll_query)
            elif payroll_query.query_type == 'deduction':
                response = self._handle_deduction_query(payroll_query)
            elif payroll_query.query_type == 'tax':
                response = self._handle_tax_query(payroll_query)
            else:
                response = self._escalate_to_hr(
                    payroll_query, 
                    f"Unknown payroll query type: {payroll_query.query_type}"
                )
            
            response.response_time = time.time() - start_time
            return response
            
        except Exception as e:
            logger.error(f"Error handling payroll query: {e}")
            return PayrollResponse(
                success=False,
                message="An error occurred while processing your payroll query. Please contact HR.",
                escalated=True,
                escalation_reason=f"System error: {str(e)}",
                response_time=time.time() - start_time
            )
    
    def _classify_payroll_query_type(self, query_lower: str) -> str:
        """Classify the type of payroll query."""
        
        # Define keyword mappings for different query types
        type_keywords = {
            'salary': ['salary', 'pay', 'paycheck', 'wages', 'compensation', 'gross pay', 'net pay'],
            'reimbursement': ['reimbursement', 'reimburse', 'expense', 'claim', 'refund', 'travel', 'medical'],
            'bonus': ['bonus', 'incentive', 'commission', 'performance pay', 'extra pay'],
            'deduction': ['deduction', 'deducted', 'withheld', 'tax', 'insurance', 'pf', 'provident fund'],
            'tax': ['tax', 'tds', 'income tax', 'tax deduction', 'tax certificate']
        }
        
        # Score each type based on keyword matches
        type_scores = {}
        for query_type, keywords in type_keywords.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                type_scores[query_type] = score
        
        # Return the type with highest score, default to 'salary'
        if type_scores:
            return max(type_scores, key=type_scores.get)
        return 'salary'
    
    def _extract_temporal_entities(self, query_lower: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract month and year from query."""
        
        # Month patterns
        months = {
            'january': '01', 'jan': '01', 'february': '02', 'feb': '02',
            'march': '03', 'mar': '03', 'april': '04', 'apr': '04',
            'may': '05', 'june': '06', 'jun': '06', 'july': '07', 'jul': '07',
            'august': '08', 'aug': '08', 'september': '09', 'sep': '09',
            'october': '10', 'oct': '10', 'november': '11', 'nov': '11',
            'december': '12', 'dec': '12'
        }
        
        month = None
        year = None
        
        # Extract month
        for month_name, month_num in months.items():
            if month_name in query_lower:
                month = month_num
                break
        
        # Extract year (4-digit years between 2020-2030)
        year_match = re.search(r'\b(202[0-9]|203[0])\b', query_lower)
        if year_match:
            year = year_match.group(1)
        
        # If no explicit year, assume current year
        if month and not year:
            year = str(datetime.now().year)
        
        return month, year
    
    def _extract_amount(self, query_lower: str) -> Optional[float]:
        """Extract monetary amount from query."""
        
        # Pattern for amounts (with or without currency symbols)
        amount_patterns = [
            r'₹\s*(\d+(?:,\d+)*(?:\.\d+)?)',  # ₹1,000 or ₹1000.50
            r'rs\.?\s*(\d+(?:,\d+)*(?:\.\d+)?)',  # Rs. 1000
            r'inr\s*(\d+(?:,\d+)*(?:\.\d+)?)',  # INR 1000
            r'(\d+(?:,\d+)*(?:\.\d+)?)\s*(?:rupees?|rs\.?|₹)',  # 1000 rupees
            r'\b(\d+(?:,\d+)*(?:\.\d+)?)\b'  # Any number
        ]
        
        for pattern in amount_patterns:
            match = re.search(pattern, query_lower)
            if match:
                amount_str = match.group(1).replace(',', '')
                try:
                    return float(amount_str)
                except ValueError:
                    continue
        
        return None
    
    def _extract_reimbursement_type(self, query_lower: str) -> Optional[str]:
        """Extract type of reimbursement from query."""
        
        reimbursement_types = {
            'travel': ['travel', 'trip', 'journey', 'transport', 'cab', 'taxi', 'flight', 'train'],
            'medical': ['medical', 'health', 'doctor', 'hospital', 'medicine', 'treatment'],
            'food': ['food', 'meal', 'lunch', 'dinner', 'breakfast', 'snack'],
            'internet': ['internet', 'wifi', 'broadband', 'data'],
            'phone': ['phone', 'mobile', 'cell', 'telephone'],
            'office': ['office', 'stationery', 'supplies', 'equipment']
        }
        
        for reimb_type, keywords in reimbursement_types.items():
            if any(keyword in query_lower for keyword in keywords):
                return reimb_type
        
        return None
    
    def _calculate_extraction_confidence(self, query_type: str, month: Optional[str], 
                                       year: Optional[str], amount: Optional[float], 
                                       reimbursement_type: Optional[str]) -> float:
        """Calculate confidence score for entity extraction."""
        
        confidence = 0.5  # Base confidence
        
        # Query type identified
        if query_type != 'salary':  # Default type
            confidence += 0.2
        
        # Temporal entities
        if month:
            confidence += 0.15
        if year:
            confidence += 0.1
        
        # Amount mentioned
        if amount:
            confidence += 0.15
        
        # Specific reimbursement type
        if reimbursement_type and query_type == 'reimbursement':
            confidence += 0.1
        
        return min(confidence, 1.0)
    
    def _handle_salary_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """Handle salary-related queries."""
        
        # For now, return placeholder response
        # TODO: Integrate with actual payroll API
        
        message = f"I understand you're asking about your salary"
        if payroll_query.month and payroll_query.year:
            message += f" for {payroll_query.month}/{payroll_query.year}"
        message += ". "
        
        if not self.api_enabled:
            message += ("I don't have access to live payroll data yet. "
                       "I've escalated your query to HR who will get back to you with the details.")
            
            return PayrollResponse(
                success=True,
                message=message,
                escalated=True,
                escalation_reason="Live payroll data not available"
            )
        
        # When API is integrated, this will fetch real data
        return PayrollResponse(
            success=True,
            message=message + "Here are your salary details...",
            data={"placeholder": "salary_data"}
        )
    
    def _handle_reimbursement_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """Handle reimbursement-related queries."""
        
        message = f"I understand you're asking about your reimbursement"
        if payroll_query.reimbursement_type:
            message += f" for {payroll_query.reimbursement_type}"
        if payroll_query.month and payroll_query.year:
            message += f" from {payroll_query.month}/{payroll_query.year}"
        message += ". "
        
        if not self.api_enabled:
            message += ("I don't have access to live reimbursement data yet. "
                       "I've escalated your query to HR who will check the status and get back to you.")
            
            return PayrollResponse(
                success=True,
                message=message,
                escalated=True,
                escalation_reason="Live reimbursement data not available"
            )
        
        return PayrollResponse(
            success=True,
            message=message + "Here's your reimbursement status...",
            data={"placeholder": "reimbursement_data"}
        )
    
    def _handle_bonus_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """Handle bonus-related queries."""
        return self._escalate_to_hr(payroll_query, "Bonus queries require HR review")
    
    def _handle_deduction_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """Handle deduction-related queries."""
        return self._escalate_to_hr(payroll_query, "Deduction queries require HR review")
    
    def _handle_tax_query(self, payroll_query: PayrollQuery) -> PayrollResponse:
        """Handle tax-related queries."""
        return self._escalate_to_hr(payroll_query, "Tax queries require HR review")
    
    def _escalate_to_hr(self, payroll_query: PayrollQuery, reason: str) -> PayrollResponse:
        """Escalate query to HR with logging."""
        
        logger.info(f"Escalating payroll query to HR: employee_id={payroll_query.employee_id}, "
                   f"type={payroll_query.query_type}, reason={reason}")
        
        message = ("I've forwarded your payroll query to our HR team. "
                  "They will review your request and get back to you within 24 hours. "
                  f"Reference: {payroll_query.employee_id}-{int(time.time())}")
        
        return PayrollResponse(
            success=True,
            message=message,
            escalated=True,
            escalation_reason=reason
        )
    
    def is_payroll_query(self, query: str) -> bool:
        """
        Determine if a query is payroll-related.
        
        Args:
            query: User query text
            
        Returns:
            True if query is payroll-related
        """
        query_lower = query.lower()
        
        payroll_keywords = [
            'salary', 'pay', 'paycheck', 'wages', 'compensation',
            'reimbursement', 'reimburse', 'expense', 'claim', 'refund',
            'bonus', 'incentive', 'commission',
            'deduction', 'deducted', 'withheld', 'tax', 'tds',
            'credited', 'credit', 'payment', 'amount'
        ]
        
        return any(keyword in query_lower for keyword in payroll_keywords)
    
    def health_check(self) -> Dict[str, Any]:
        """Return service health status."""
        return {
            "service": "PayrollService",
            "status": "healthy",
            "api_enabled": self.api_enabled,
            "escalation_threshold": self.escalation_threshold,
            "timestamp": datetime.now().isoformat()
        }
