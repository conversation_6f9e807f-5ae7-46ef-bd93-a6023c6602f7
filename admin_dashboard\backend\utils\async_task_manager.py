"""
Background task manager for admin dashboard operations
"""
import asyncio
import sqlite3
import datetime
import time
import threading
from typing import Dict, Any, Optional
from .email_service import EmailService

class EscalationReminderManager:
    """Manages escalation reminders and notifications"""
    
    def __init__(self, escalation_db_path: str, check_interval_minutes: int = 30):
        self.escalation_db_path = escalation_db_path
        self.check_interval_minutes = check_interval_minutes
        self.running = False
        self.thread = None
        self.email_service = EmailService()
        
    def start(self):
        """Start the reminder manager in a background thread"""
        if self.running:
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_reminder_loop, daemon=True)
        self.thread.start()
        print("Escalation reminder manager started")
        
    def stop(self):
        """Stop the reminder manager"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        print("Escalation reminder manager stopped")
        
    def _run_reminder_loop(self):
        """Main loop for checking and sending reminders"""
        while self.running:
            try:
                self._check_and_send_reminders()
                # Sleep for the specified interval
                time.sleep(self.check_interval_minutes * 60)
            except Exception as e:
                print(f"Error in reminder loop: {e}")
                time.sleep(60)  # Sleep for 1 minute on error
                
    def _check_and_send_reminders(self):
        """Check for escalations that need reminders and send them"""
        try:
            with sqlite3.connect(self.escalation_db_path) as conn:
                cursor = conn.cursor()
                
                # Get escalations that are due in the next 20 hours
                current_time = datetime.datetime.utcnow()
                reminder_threshold = current_time + datetime.timedelta(hours=20)
                
                cursor.execute('''
                    SELECT id, assigned_admin, due_date, issue_type, issue_description, 
                           sender_email, sender_name, routing_audit_log
                    FROM escalations 
                    WHERE assigned_admin IS NOT NULL 
                    AND due_date IS NOT NULL 
                    AND status IN ('routed', 'assigned')
                    AND due_date <= ?
                ''', (reminder_threshold.isoformat(),))
                
                escalations = cursor.fetchall()
                
                for escalation in escalations:
                    escalation_id, assigned_admin, due_date, issue_type, issue_description, sender_email, sender_name, routing_audit_log = escalation
                    
                    # Check if reminder was already sent (within last 4 hours)
                    if self._should_send_reminder(escalation_id, routing_audit_log):
                        self._send_reminder(
                            escalation_id, assigned_admin, due_date, issue_type, 
                            issue_description, sender_email, sender_name
                        )
                        
        except Exception as e:
            print(f"Error checking reminders: {e}")
            
    def _should_send_reminder(self, escalation_id: int, routing_audit_log: str) -> bool:
        """Check if a reminder should be sent for this escalation"""
        if not routing_audit_log:
            return True
            
        try:
            import json
            audit_log = json.loads(routing_audit_log)
            
            # Check if reminder was sent in the last 4 hours
            current_time = datetime.datetime.utcnow()
            four_hours_ago = current_time - datetime.timedelta(hours=4)
            
            for entry in audit_log:
                if entry.get('action') == 'reminder_sent':
                    reminder_time = datetime.datetime.fromisoformat(entry['timestamp'])
                    if reminder_time > four_hours_ago:
                        return False
                        
            return True
        except:
            return True
            
    def _send_reminder(self, escalation_id: int, assigned_admin: str, due_date: str, 
                       issue_type: str, issue_description: str, sender_email: str, sender_name: str):
        """Send reminder to assigned admin"""
        try:
            # Calculate time until due
            due_datetime = datetime.datetime.fromisoformat(due_date)
            current_time = datetime.datetime.utcnow()
            time_until_due = due_datetime - current_time
            
            hours_until_due = int(time_until_due.total_seconds() / 3600)
            
            # Send reminder email
            subject = f"URGENT: HR Query Reminder - Due in {hours_until_due} hours"
            body = f"""
Hello,

This is a reminder that you have an HR query that is due soon.

Query ID: {escalation_id}
Issue Type: {issue_type}
Issue: {issue_description}
Due Date: {due_date}
Time Remaining: {hours_until_due} hours

Employee: {sender_name} ({sender_email})

Please ensure this query is addressed before the due date.

Best regards,
HR System
            """.strip()
            
            result = self.email_service.send_email([assigned_admin], subject, body)
            
            if result.get("success"):
                # Log the reminder in audit log
                self._log_reminder_sent(escalation_id, assigned_admin)
                print(f"Reminder sent for escalation {escalation_id} to {assigned_admin}")
            else:
                print(f"Failed to send reminder for escalation {escalation_id}: {result.get('message')}")
                
        except Exception as e:
            print(f"Error sending reminder for escalation {escalation_id}: {e}")
            
    def _log_reminder_sent(self, escalation_id: int, admin_email: str):
        """Log that a reminder was sent in the audit log"""
        try:
            with sqlite3.connect(self.escalation_db_path) as conn:
                cursor = conn.cursor()
                
                # Get existing audit log
                cursor.execute('SELECT routing_audit_log FROM escalations WHERE id = ?', (escalation_id,))
                row = cursor.fetchone()
                
                if row and row[0]:
                    import json
                    audit_log = json.loads(row[0])
                else:
                    audit_log = []
                
                # Add reminder entry
                reminder_entry = {
                    "timestamp": datetime.datetime.utcnow().isoformat(),
                    "action": "reminder_sent",
                    "to_admin": admin_email,
                    "type": "due_date_reminder"
                }
                
                audit_log.append(reminder_entry)
                
                # Update audit log
                cursor.execute('''
                    UPDATE escalations 
                    SET routing_audit_log = ? 
                    WHERE id = ?
                ''', (json.dumps(audit_log), escalation_id))
                conn.commit()
                
        except Exception as e:
            print(f"Error logging reminder: {e}")

# Global instance
reminder_manager: Optional[EscalationReminderManager] = None

def get_reminder_manager(escalation_db_path: str) -> EscalationReminderManager:
    """Get or create the global reminder manager instance"""
    global reminder_manager
    if reminder_manager is None:
        reminder_manager = EscalationReminderManager(escalation_db_path)
    return reminder_manager

def start_reminder_manager(escalation_db_path: str):
    """Start the reminder manager"""
    manager = get_reminder_manager(escalation_db_path)
    manager.start()

def stop_reminder_manager():
    """Stop the reminder manager"""
    global reminder_manager
    if reminder_manager:
        reminder_manager.stop()
        reminder_manager = None
