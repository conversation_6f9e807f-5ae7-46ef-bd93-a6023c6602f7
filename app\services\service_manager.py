"""
Centralized service manager for singleton pattern implementation.
Provides lazy loading and dependency injection for all application services.
"""
import asyncio
import concurrent.futures
from typing import Optional, Dict, Any
from pathlib import Path

from transformers import AutoTokenizer

from src.utils.logger import get_logger
from src.utils.device_manager import get_optimal_device, get_device_info
from src.utils.tokenizer_registry import get_bge_tokenizer_function
from src.config import EMBEDDING_TOKENIZER_DIR, ENABLE_EMAIL_ESCALATION, HR_EMAILS, DEV_LOGGING
from src.chain.chain_builder import ChainBuilder
from src.conversation.history_manager import HistoryManager
from src.speech.speech_to_text import SpeechToText
from src.speech.text_to_speech import TextToSpeech
from src.user_authentication.user_authorisation import AuthService
from src.utils.email_service import EmailService
from src.retrieval.vector_search import VectorSearch
from src.retrieval.context_builder import ContextBuilder
from src.database.user_db import UserModel
from src.database.vector_store import QdrantVectorStore
from src.document_processing.embedding_generator import EmbeddingGenerator
from src.document_processing.training_pipeline import Training<PERSON><PERSON>eline
from src.intent.intent_classifier import IntentClassifier
from app.services.payroll_service import PayrollService

logger = get_logger(__name__)


class ServiceManager:
    """
    Centralized singleton service manager with lazy loading and error handling.
    Ensures optimal startup time and resource management.
    """
    
    _instance: Optional['ServiceManager'] = None
    _services: Dict[str, Any] = {}
    _session_history: Dict[str, Any] = {}
    _app_state: Dict[str, Any] = {}
    _initialized = False
    
    def __new__(cls) -> 'ServiceManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        # Only initialize once
        if self._initialized:
            return
            
        self._initialized = True
        logger.info("ServiceManager singleton initialized")
    
    @classmethod
    def get_instance(cls) -> 'ServiceManager':
        """Get the singleton instance."""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    def get_user_model(self) -> UserModel:
        """Get or create UserModel singleton."""
        if 'user_model' not in self._services:
            logger.info("Initializing UserModel...")
            self._services['user_model'] = UserModel()
            logger.info("UserModel initialized successfully")
        return self._services['user_model']
    
    def get_vector_search(self) -> VectorSearch:
        """Get or create VectorSearch singleton."""
        if 'vector_search' not in self._services:
            logger.info("Initializing VectorSearch...")
            self._services['vector_search'] = VectorSearch()
            logger.info("VectorSearch initialized successfully")
        return self._services['vector_search']
    
    def get_context_builder(self) -> ContextBuilder:
        """Get or create ContextBuilder singleton."""
        if 'context_builder' not in self._services:
            if DEV_LOGGING['verbose_startup']:
                logger.info("Initializing ContextBuilder...")
            
            # Use centralized tokenizer registry
            token_count_fn = get_bge_tokenizer_function()
            
            self._services['context_builder'] = ContextBuilder(
                vector_search=self.get_vector_search(), 
                tokenizer=token_count_fn
            )
            
            if DEV_LOGGING['verbose_startup']:
                logger.info("ContextBuilder initialized successfully")
        return self._services['context_builder']
    
    def get_chain_builder(self) -> ChainBuilder:
        """Get or create ChainBuilder singleton."""
        if 'chain_builder' not in self._services:
            logger.info("Initializing ChainBuilder...")
            self._services['chain_builder'] = ChainBuilder(
                context_builder=self.get_context_builder()
            )
            logger.info("ChainBuilder initialized successfully")
        return self._services['chain_builder']
    
    def get_history_manager(self) -> HistoryManager:
        """Get or create HistoryManager singleton."""
        if 'history_manager' not in self._services:
            logger.info("Initializing HistoryManager...")
            self._services['history_manager'] = HistoryManager()
            logger.info("HistoryManager initialized successfully")
        return self._services['history_manager']
    
    def get_speech_to_text(self, use_whisper: bool = True) -> SpeechToText:
        """Get or create SpeechToText singleton."""
        service_key = f'speech_to_text_{use_whisper}'
        if service_key not in self._services:
            logger.info(f"Initializing SpeechToText (whisper={use_whisper})...")
            self._services[service_key] = SpeechToText(use_whisper=use_whisper)
            logger.info("SpeechToText initialized successfully")
        return self._services[service_key]
    
    def get_text_to_speech(self) -> TextToSpeech:
        """Get or create TextToSpeech singleton."""
        if 'text_to_speech' not in self._services:
            logger.info("Initializing TextToSpeech...")
            self._services['text_to_speech'] = TextToSpeech()
            logger.info("TextToSpeech initialized successfully")
        return self._services['text_to_speech']
    
    def get_auth_service(self) -> AuthService:
        """Get or create AuthService singleton."""
        if 'auth_service' not in self._services:
            logger.info("Initializing AuthService...")
            auth_service = AuthService()
            # Patch the AuthService instance to use the singleton UserModel
            auth_service.user_model = self.get_user_model()
            self._services['auth_service'] = auth_service
            logger.info("AuthService initialized successfully")
        return self._services['auth_service']
    
    def get_email_service(self) -> Optional[EmailService]:
        """Get or create EmailService singleton if enabled."""
        if 'email_service' not in self._services:
            if ENABLE_EMAIL_ESCALATION and HR_EMAILS:
                logger.info("Initializing EmailService...")
                self._services['email_service'] = EmailService()
                logger.info("EmailService initialized successfully")
            else:
                logger.info("EmailService disabled - email escalation not configured")
                self._services['email_service'] = None
        return self._services['email_service']
    
    def get_vector_store(self) -> QdrantVectorStore:
        """Get or create QdrantVectorStore singleton."""
        if 'vector_store' not in self._services:
            logger.info("Initializing QdrantVectorStore...")
            self._services['vector_store'] = QdrantVectorStore()
            logger.info("QdrantVectorStore initialized successfully")
        return self._services['vector_store']
    
    def get_embedding_generator(self) -> EmbeddingGenerator:
        """Get or create EmbeddingGenerator singleton."""
        if 'embedding_generator' not in self._services:
            if DEV_LOGGING['verbose_startup']:
                logger.info("Initializing EmbeddingGenerator...")
            self._services['embedding_generator'] = EmbeddingGenerator()
            if DEV_LOGGING['verbose_startup']:
                logger.info("EmbeddingGenerator initialized successfully (lazy loading enabled)")
        return self._services['embedding_generator']
    
    def get_training_pipeline(self) -> TrainingPipeline:
        """Get or create TrainingPipeline singleton with lazy initialization."""
        if 'training_pipeline' not in self._services:
            logger.info("Registering TrainingPipeline service (lazy initialization enabled)")
            # Only register the service, don't initialize heavy dependencies yet
            self._services['training_pipeline'] = None
            logger.info("TrainingPipeline service registered - will initialize on first use")
        return self._services['training_pipeline']
    
    def _initialize_training_pipeline(self) -> TrainingPipeline:
        """Internal method to actually initialize TrainingPipeline when needed."""
        if self._services.get('training_pipeline') is None:
            logger.info("Initializing TrainingPipeline on first use...")
            try:
                pipeline = TrainingPipeline(
                    vector_store=self.get_vector_store(),
                    embedding_generator=self.get_embedding_generator()
                )
                self._services['training_pipeline'] = pipeline
                logger.info("TrainingPipeline initialized successfully")
            except Exception as e:
                logger.error(f"TrainingPipeline initialization failed: {e}")
                self._services['training_pipeline'] = None
        return self._services['training_pipeline']
    
    def use_training_pipeline(self) -> TrainingPipeline:
        """Get and initialize TrainingPipeline when actually needed."""
        pipeline = self.get_training_pipeline()
        if pipeline is None:
            pipeline = self._initialize_training_pipeline()
        return pipeline
    
    def get_intent_classifier(self) -> Optional[IntentClassifier]:
        """Get or create IntentClassifier singleton with proper async loading and retry mechanism."""
        if 'intent_classifier' not in self._services:
            if DEV_LOGGING['verbose_startup']:
                logger.info("Initializing IntentClassifier...")
            try:
                # Initialize with timeout and retry mechanism
                intent_classifier = IntentClassifier()
                
                # Force load the model immediately to prevent hanging later
                if hasattr(intent_classifier, '_load_or_train_model'):
                    logger.info("Forcing model loading during initialization...")
                    
                    # Add timeout to prevent hanging
                    import signal
                    import threading
                    
                    def timeout_handler():
                        raise TimeoutError("Intent classifier model loading timed out")
                    
                    # Set a timeout for model loading
                    timer = threading.Timer(30.0, timeout_handler)  # 30 second timeout
                    timer.start()
                    
                    try:
                        intent_classifier._load_or_train_model(lazy=False)
                        timer.cancel()
                        logger.info("IntentClassifier model loaded successfully during initialization")
                    except TimeoutError:
                        timer.cancel()
                        logger.error("Intent classifier model loading timed out")
                        raise
                    except Exception as e:
                        timer.cancel()
                        raise
                else:
                    logger.warning("_load_or_train_model method not found")
                
                # Verify the model is actually loaded
                if hasattr(intent_classifier, 'model') and intent_classifier.model is not None:
                    logger.info("IntentClassifier model verified and ready")
                    self._services['intent_classifier'] = intent_classifier
                    if DEV_LOGGING['verbose_startup']:
                        logger.info("IntentClassifier initialized successfully (model pre-loaded)")
                else:
                    logger.error("IntentClassifier model failed to load properly")
                    self._services['intent_classifier'] = None
                    
            except Exception as e:
                logger.error(f"Failed to initialize IntentClassifier: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                self._services['intent_classifier'] = None
        return self._services['intent_classifier']
    
    def get_session_history(self) -> Dict[str, Any]:
        """Get session history storage."""
        return self._session_history
    
    def get_app_state(self) -> Dict[str, Any]:
        """Get application state storage."""
        return self._app_state
    
    def run_async_in_sync(self, coro):
        """Execute async coroutine in sync context with proper error handling."""
        try:
            # Try to get the current event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If loop is running, use ThreadPoolExecutor
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, coro)
                        return future.result(timeout=120)  # 120 second timeout
                else:
                    # If loop exists but not running, use it
                    return loop.run_until_complete(coro)
            except RuntimeError:
                # No event loop in current thread, create new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(coro)
                finally:
                    loop.close()
                    asyncio.set_event_loop(None)
                    
        except concurrent.futures.TimeoutError:
            logger.error("Async execution timed out after 120 seconds")
            return {
                "content": "I'm sorry, the request is taking too long. Please try again.",
                "language": "en",
                "sources": [],
                "error": {"type": "TimeoutError"}
            }
        except Exception as e:
            logger.error(f"Async execution failed: {e}")
            return {
                "content": "I'm sorry, something went wrong. Please try again.",
                "language": "en",
                "sources": [],
                "error": {"type": "ExecutionError", "message": str(e)}
            }
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on all services."""
        import time
        health_status = {
            "status": "healthy",
            "services": {},
            "timestamp": str(time.time())
        }
        
        # Check each service
        for service_name in ['user_model', 'vector_search', 'chain_builder', 'auth_service']:
            try:
                service = self._services.get(service_name)
                health_status["services"][service_name] = {
                    "status": "loaded" if service else "not_loaded",
                    "healthy": service is not None
                }
            except Exception as e:
                health_status["services"][service_name] = {
                    "status": "error",
                    "healthy": False,
                    "error": str(e)
                }
        
        # Overall health
        all_healthy = all(
            svc.get("healthy", False) 
            for svc in health_status["services"].values()
        )
        health_status["status"] = "healthy" if all_healthy else "degraded"
        
        return health_status
    
    def cleanup(self):
        """Clean up all services and resources."""
        logger.info("Cleaning up services...")
        self._services.clear()
        self._session_history.clear()
        self._app_state.clear()
        logger.info("Service cleanup completed")
