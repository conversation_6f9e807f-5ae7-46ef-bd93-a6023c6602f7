import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider"; // Assume stub
import { Skeleton } from "@/components/ui/skeleton";
import api from "@/services/api";

const PAGE_SIZE = 10;

const columns = [
  { key: "query", label: "Raw Query" },
  { key: "timestamp", label: "Timestamp" },
  { key: "intent", label: "Auto-Detected Intent" },
  { key: "confidence", label: "Confidence" },
  { key: "retrain", label: "Retrain?" },
];

const MisunderstoodQueries = () => {
  const [dateRange, setDateRange] = useState("7d");
  const [confidence, setConfidence] = useState(0.5);
  const [search, setSearch] = useState("");
  const [page, setPage] = useState(1);
  const [data, setData] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retrainIds, setRetrainIds] = useState<{ [id: string]: boolean }>({});

  useEffect(() => {
    setLoading(true);
    setError(null);
    const params = [
      `range=${dateRange}`,
      `confidence=${confidence}`,
      search ? `query=${encodeURIComponent(search)}` : null,
      `page=${page}`,
      `pageSize=${PAGE_SIZE}`,
    ]
      .filter(Boolean)
      .join("&");
    api
      .get(`/training/misunderstood-queries?${params}`)
      .then((res) => {
        setData(res.data.queries || []);
        setTotal(res.data.total || 0);
      })
      .catch((err) => setError("Failed to load data"))
      .finally(() => setLoading(false));
  }, [dateRange, confidence, search, page]);

  const handleRetrain = (id: string) => {
    setRetrainIds((prev) => ({ ...prev, [id]: !prev[id] }));

    // Send retrain request to the backend
    const isCurrentlyFlagged = retrainIds[id];
    const action = isCurrentlyFlagged ? 'unflag' : 'flag';

    api.post('/api/training/flag-for-retraining', {
      query_id: id,
      action: action
    })
    .then(() => {
      console.log(`Successfully ${action}ged query ${id} for retraining`);
    })
    .catch((err) => {
      console.error(`Failed to ${action} query for retraining:`, err);
      // Revert the UI state if the API call failed
      setRetrainIds((prev) => ({ ...prev, [id]: !prev[id] }));
    });
  };

  if (loading) return <Skeleton className="h-80 w-full" />;
  if (error) return <div className="text-red-500 p-4">{error}</div>;
  if (!data.length) return <div className="text-muted-foreground p-4">No data available.</div>;

  const totalPages = Math.ceil(total / PAGE_SIZE);

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <CardTitle>Misunderstood Queries</CardTitle>
          <div className="flex flex-wrap gap-2 items-center">
            <Input
              type="date"
              value={dateRange}
              onChange={e => setDateRange(e.target.value)}
              className="w-40"
            />
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">Confidence ≥</span>
              <Slider min={0} max={1} step={0.01} value={confidence} onValueChange={setConfidence} className="w-24" />
              <span className="text-xs font-mono">{confidence.toFixed(2)}</span>
            </div>
            <Input
              type="text"
              placeholder="Search queries..."
              value={search}
              onChange={e => { setSearch(e.target.value); setPage(1); }}
              className="w-56"
            />
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
              <thead className="bg-muted dark:bg-zinc-800">
                <tr>
                  {columns.map(col => (
                    <th key={col.key} className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">
                      {col.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-background dark:bg-zinc-900">
                {data.map((row, idx) => (
                  <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                    <td className="px-4 py-2 text-sm">{row.query}</td>
                    <td className="px-4 py-2 text-sm">{row.timestamp}</td>
                    <td className="px-4 py-2 text-sm">{row.intent}</td>
                    <td className="px-4 py-2 text-sm">{row.confidence?.toFixed(2)}</td>
                    <td className="px-4 py-2 text-sm">
                      <Button
                        size="sm"
                        variant={retrainIds[row.id] ? "default" : "outline"}
                        onClick={() => handleRetrain(row.id)}
                        aria-pressed={!!retrainIds[row.id]}
                      >
                        {retrainIds[row.id] ? "Flagged" : "Retrain?"}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {/* Pagination controls */}
          <div className="flex justify-end gap-2 items-center mt-4">
            <Button size="sm" variant="ghost" disabled={page === 1} onClick={() => setPage(page - 1)}>
              Previous
            </Button>
            <span className="text-xs text-muted-foreground">
              Page {page} of {totalPages || 1}
            </span>
            <Button size="sm" variant="ghost" disabled={page === totalPages || totalPages === 0} onClick={() => setPage(page + 1)}>
              Next
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MisunderstoodQueries; 