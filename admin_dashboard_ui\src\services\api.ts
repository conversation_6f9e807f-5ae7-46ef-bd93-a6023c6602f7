import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { API_BASE_URL } from "../apiConfig";
import { useAuthStore } from "../hooks/useAuthStore";
import toast from "react-hot-toast";

// Extend AxiosRequestConfig to allow 'metadata' property
declare module 'axios' {
  export interface InternalAxiosRequestConfig {
    metadata?: { startTime: Date };
  }
}

// ============================================================================
// AXIOS INSTANCE CONFIGURATION
// ============================================================================

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// ============================================================================
// REQUEST INTERCEPTOR - Add JWT Token
// ============================================================================

api.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().token;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request timestamp for performance monitoring
    config.metadata = { startTime: new Date() };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// ============================================================================
// RESPONSE INTERCEPTOR - Handle Errors & Token Refresh
// ============================================================================

api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Calculate request duration
    const endTime = new Date();
    const duration = response.config.metadata && response.config.metadata.startTime ? endTime.getTime() - response.config.metadata.startTime.getTime() : 0;

    // Log slow requests (> 2 seconds)
    if (duration > 2000) {
      console.warn(`Slow API request: ${response.config.url} took ${duration}ms`);
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    // Handle 401 Unauthorized - Token expired
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh token
        const refreshResponse = await api.post('/auth/refresh');
        const newToken = refreshResponse.data.access_token;

        // Update token in store
        useAuthStore.getState().setToken(newToken);

        // Retry original request with new token
        originalRequest.headers.Authorization = `Bearer ${newToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        // Refresh failed, redirect to login
        useAuthStore.getState().logout();
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle different error types
    if (error.response?.status === 403) {
      toast.error('Access denied. You do not have permission to perform this action.');
    } else if (error.response?.status === 429) {
      toast.error('Rate limit exceeded. Please try again later.');
    } else if (typeof error.response?.status === 'number' && error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please check your connection.');
    } else if (!error.response) {
      toast.error('Network error. Please check your connection.');
    }

    return Promise.reject(error);
  }
);

// ============================================================================
// GENERIC API HELPERS
// ============================================================================

export const get = <T = any>(url: string, config?: any): Promise<AxiosResponse<T>> =>
  api.get(url, config);

export const post = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>
  api.post(url, data, config);

export const put = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>
  api.put(url, data, config);

export const patch = <T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> =>
  api.patch(url, data, config);

export const del = <T = any>(url: string, config?: any): Promise<AxiosResponse<T>> =>
  api.delete(url, config);

// ============================================================================
// AUTHENTICATION API
// ============================================================================

export const authApi = {
  // COMMENTED OUT: OTP functionality temporarily disabled
  // requestOtp: (email: string) =>
  //   post('/api/auth/request-otp', { email }),

  // login: (email: string, otp: string) =>
  //   post('/admin/verify-otp', { email, otp }),

  // NEW: 2FA-based authentication
  check2FAStatus: (email: string) =>
    post('/admin/check-2fa-status', { email }),

  request2FASetup: (email: string) =>
    post('/admin/request-2fa-setup', { email }),

  verify2FA: (email: string, totpCode: string) =>
    post('/admin/verify-2fa', { email, totp: totpCode }),

  logout: () =>
    post('/auth/logout'),

  refreshToken: () =>
    post('/auth/refresh'),

  enable2FA: () =>
    post('/auth/2fa/enable'),

  disable2FA: (otp: string) =>
    post('/auth/2fa/disable', { otp }),

  generateBackupCodes: () =>
    post('/auth/backup-codes/generate'),

  verifyBackupCode: (code: string) =>
    post('/auth/backup-codes/verify', { code }),
};

// ============================================================================
// USER MANAGEMENT API
// ============================================================================

export const userApi = {
  getCurrentUser: () =>
    get('/api/user'),

  updateProfile: (data: any) =>
    patch('/api/user/profile', data),

  updatePreferences: (preferences: any) =>
    patch('/api/user/preferences', preferences),

  changePassword: (currentPassword: string, newPassword: string) =>
    post('/api/user/change-password', { current_password: currentPassword, new_password: newPassword }),

  uploadAvatar: (file: File) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return post('/api/user/avatar', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  },
};

// ============================================================================
// ESCALATION API
// ============================================================================

export const escalationApi = {
  getPending: () =>
    get('/escalations/pending'),

  getEscalation: (id: string) =>
    get(`/escalations/${id}`),

  assignEscalation: (id: string, data: any) =>
    patch(`/escalations/${id}/assign`, data),

  routeEscalation: (id: string, data: any) =>
    post(`/escalations/${id}/route`, data),

  replyToEscalation: (id: string, data: any) =>
    post(`/escalations/${id}/reply`, data),

  getRoutingHistory: (id: string) =>
    get(`/escalations/${id}/routing-history`),
};

// ============================================================================
// ADMIN MANAGEMENT API
// ============================================================================

export const adminApi = {
  getUsers: (params?: any) =>
    get('/admin/users', { params }),

  createUser: (userData: any) =>
    post('/admin/users', userData),

  updateUser: (userId: string, userData: any) =>
    patch(`/admin/users/${userId}`, userData),

  deleteUser: (userId: string) =>
    del(`/admin/users/${userId}`),

  changeRole: (email: string, newRole: string) =>
    post('/admin/change-role', { email, role: newRole }),

  inviteUser: (email: string, role: string, organizationId: string) =>
    post('/admin/invite', { email, role, organization_id: organizationId }),

  revokeInvitation: (invitationId: string) =>
    del(`/admin/invitations/${invitationId}`),

  getInvitations: () =>
    get('/admin/invitations'),

  getAvailableForRouting: () =>
    get('/admin-users/available-for-routing'),
};

// ============================================================================
// CHATBOT METRICS & ANALYTICS API
// ============================================================================

export const metricsApi = {
  getDashboardMetrics: (timeRange?: string) =>
    get('/api/chat-analytics/live', { params: { time_range: timeRange } }),
  getChatbotMetrics: (timeRange?: string) =>
    get('/api/metrics/chatbot', { params: { time_range: timeRange } }),
  getPerformanceMetrics: (timeRange?: string) =>
    get('/api/metrics/performance', { params: { time_range: timeRange } }),
  getUserEngagement: (timeRange?: string) =>
    get('/api/metrics/engagement', { params: { time_range: timeRange } }),
  getSentimentAnalysis: (timeRange?: string) =>
    get('/api/metrics/sentiment', { params: { time_range: timeRange } }),
  getIntentMetrics: (timeRange?: string) =>
    get('/api/metrics/intents', { params: { time_range: timeRange } }),
  getAnomalies: (severity?: string, timeRange?: string) =>
    get('/api/sessions/anomalies', { params: { severity, range: timeRange } }),
  acknowledgeAnomaly: (anomalyId: string, notes?: string) =>
    post(`/api/metrics/anomalies/${anomalyId}/acknowledge`, { notes }),
  // Real-time live data endpoints
  getLiveSessions: () =>
    get('/api/sessions/live'),
  getActiveEscalations: () =>
    get('/api/escalations/pending'),
  getRealTimeFeedback: (timeRange?: string) =>
    get('/api/feedback/trends', { params: { range: timeRange } }),
  getTopQuestions: (timeRange?: string, limit?: number) =>
    get('/api/analytics/top-questions', { params: { range: timeRange, limit } }),
  getResponseTimes: (timeRange?: string) =>
    get('/api/analytics/response-times', { params: { range: timeRange } }),
  getSystemHealth: () =>
    get('/api/metrics/system-health'),
  getModelPerformance: (timeRange?: string) =>
    get('/api/metrics/model-performance', { params: { time_range: timeRange } }),
};

// ============================================================================
// CHAT LOGS & SESSIONS API
// ============================================================================

export const chatApi = {
  getAllChats: (params?: any) =>
    get('/api/all-chats', { params }),
  getChatLogs: (params?: any) =>
    get('/api/chatlogs', { params }), // Use proxy endpoint
  getChatSession: (sessionId: string) =>
    get(`/api/chat-sessions/${sessionId}`),
  getChatMessages: (sessionId: string) =>
    get(`/api/chat-sessions/${sessionId}/messages`),
  getQueries: (params?: any) =>
    get('/api/queries', { params }),
  getChatTrends: (timeRange?: string) =>
    get('/api/chat-trends', { params: { time_range: timeRange } }),
  getChatTypes: () =>
    get('/api/chat-types'),
  escalateChat: (sessionId: string, reason: string, priority: string) =>
    post(`/api/chat-sessions/${sessionId}/escalate`, { reason, priority }),
  resolveChat: (sessionId: string, resolution: string) =>
    post(`/api/chat-sessions/${sessionId}/resolve`, { resolution }),
  addChatNote: (sessionId: string, note: string) =>
    post(`/api/chat-sessions/${sessionId}/notes`, { note }),
};

// ============================================================================
// AUDIT & COMPLIANCE API
// ============================================================================

export const auditApi = {
  getAuditLogs: (params?: any) =>
    get('/audit/logs', { params }),

  getDeviceLogs: (params?: any) =>
    get('/devices/logs', { params }),

  getSessions: (params?: any) =>
    get('/sessions', { params }),

  revokeSession: (sessionData: any) =>
    post('/sessions/revoke', sessionData),

  getComplianceReport: (type: string, timeRange?: string) =>
    get(`/api/compliance/${type}`, { params: { time_range: timeRange } }),

  requestDataDeletion: (userId: string, reason: string) =>
    post('/api/compliance/data-deletion', { user_id: userId, reason }),

  exportUserData: (userId: string, format: string) =>
    post('/api/compliance/export-user-data', { user_id: userId, format }),

  getGDPRRequests: () =>
    get('/api/compliance/gdpr-requests'),
};

// ============================================================================
// FEATURE REQUESTS & FEEDBACK API
// ============================================================================

export const feedbackApi = {
  getFeatureRequests: (params?: any) =>
    get('/api/feature-requests', { params }),

  createFeatureRequest: (requestData: any) =>
    post('/api/feature-requests', requestData),

  updateFeatureRequest: (requestId: string, updateData: any) =>
    patch(`/api/feature-requests/${requestId}`, updateData),

  voteFeatureRequest: (requestId: string) =>
    post(`/api/feature-requests/${requestId}/vote`),

  addComment: (requestId: string, comment: string) =>
    post(`/api/feature-requests/${requestId}/comments`, { comment }),

  getFeedbackTrends: (timeRange?: string) =>
    get('/api/feedback/trends', { params: { time_range: timeRange } }),

  getEscalatedFeedback: () =>
    get('/api/feedback/escalated'),

  getResolutionMetrics: () =>
    get('/api/feedback/resolution'),
};

// ============================================================================
// API KEYS & WEBHOOKS API
// ============================================================================

export const integrationApi = {
  getApiKeys: () =>
    get('/api/integrations/api-keys'),

  createApiKey: (keyData: any) =>
    post('/api/integrations/api-keys', keyData),

  revokeApiKey: (keyId: string) =>
    del(`/api/integrations/api-keys/${keyId}`),

  getApiKeyUsage: (keyId: string) =>
    get(`/api/integrations/api-keys/${keyId}/usage`),

  getWebhooks: () =>
    get('/api/integrations/webhooks'),

  createWebhook: (webhookData: any) =>
    post('/api/integrations/webhooks', webhookData),

  updateWebhook: (webhookId: string, webhookData: any) =>
    patch(`/api/integrations/webhooks/${webhookId}`, webhookData),

  deleteWebhook: (webhookId: string) =>
    del(`/api/integrations/webhooks/${webhookId}`),

  testWebhook: (webhookId: string) =>
    post(`/api/integrations/webhooks/${webhookId}/test`),

  getWebhookDeliveries: (webhookId: string) =>
    get(`/api/integrations/webhooks/${webhookId}/deliveries`),
};

// ============================================================================
// SYSTEM HEALTH & MONITORING API
// ============================================================================

export const systemApi = {
  getSystemHealth: () =>
    get('/api/system/health'),

  getPerformanceMetrics: (timeRange?: string) =>
    get('/api/system/performance', { params: { time_range: timeRange } }),

  getServiceStatus: () =>
    get('/api/system/services'),

  getDocumentHealth: () =>
    get('/api/health/documents'),

  getUptime: () =>
    get('/api/system/uptime'),

  getErrorLogs: (params?: any) =>
    get('/api/system/errors', { params }),

  getSlowQueries: () =>
    get('/api/system/slow-queries'),

  clearCache: (cacheType?: string) =>
    post('/api/system/cache/clear', { cache_type: cacheType }),
};

// ============================================================================
// EXPORT & REPORTING API
// ============================================================================

export const exportApi = {
  requestExport: (exportData: any) =>
    post('/api/exports', exportData),

  getExportStatus: (exportId: string) =>
    get(`/api/exports/${exportId}`),

  downloadExport: (exportId: string) =>
    get(`/api/exports/${exportId}/download`, { responseType: 'blob' }),

  getReports: () =>
    get('/api/reports'),

  createReport: (reportData: any) =>
    post('/api/reports', reportData),

  updateReport: (reportId: string, reportData: any) =>
    patch(`/api/reports/${reportId}`, reportData),

  deleteReport: (reportId: string) =>
    del(`/api/reports/${reportId}`),

  generateReport: (reportId: string) =>
    post(`/api/reports/${reportId}/generate`),
};

// ============================================================================
// LEGACY API ENDPOINTS (for backward compatibility)
// ============================================================================

export const legacyApi = {
  getStatsUsers: () => get('/stats/users'),
  getStatsUsage: () => get('/stats/usage'),
  getStatsIntents: () => get('/stats/intents'),
  getOverrides: () => get('/api/overrides'),
  getHRRepresentatives: () => get('/api/hr-representatives'),
};

// ============================================================================
// EXPORT DEFAULT API INSTANCE
// ============================================================================

export default api;

// Add missing API exports for dashboard
export const getChatLogs = chatApi.getChatLogs;
export const getChatTrends = chatApi.getChatTrends;
export const getAuditLogs = auditApi.getAuditLogs;
export const getDeviceLogs = auditApi.getDeviceLogs;
export const getStatsUsers = legacyApi.getStatsUsers;
export const getStatsUsage = legacyApi.getStatsUsage;
export const getStatsIntents = legacyApi.getStatsIntents;
export const changeRole = adminApi.changeRole;