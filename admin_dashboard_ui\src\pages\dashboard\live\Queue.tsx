import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { escalationApi, adminApi } from "@/services/api";

const TABS = ["Queue", "In-Progress"];

interface EscalationItem {
  id: string;
  user: string;
  timestamp: string;
  topic: string;
  status: string;
  assigned_admin?: string;
  due_date?: string;
  type?: 'queue' | 'session';
  started?: string;
  chat?: any[];
}

interface AdminUser {
  email: string;
  full_name: string;
  role: string;
  active: boolean;
}

const LiveHandover = () => {
  const [tab, setTab] = useState("Queue");
  const [queue, setQueue] = useState<EscalationItem[]>([]);
  const [sessions, setSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Reply modal state
  const [showReplyDialog, setShowReplyDialog] = useState(false);
  const [selectedQueueItem, setSelectedQueueItem] = useState<EscalationItem | null>(null);
  const [replyText, setReplyText] = useState("");
  
  // Route modal state
  const [showRouteDialog, setShowRouteDialog] = useState(false);
  const [selectedRouteItem, setSelectedRouteItem] = useState<EscalationItem | null>(null);
  const [availableAdmins, setAvailableAdmins] = useState<AdminUser[]>([]);
  const [selectedAdmin, setSelectedAdmin] = useState("");
  const [routeReason, setRouteReason] = useState("");
  const [dueDate, setDueDate] = useState("");
  const [routingLoading, setRoutingLoading] = useState(false);

  // Quick reply templates
  const quickReplies = [
    "Thank you for your inquiry. I'll look into this and get back to you shortly.",
    "I understand your concern. Let me investigate this matter and provide you with a detailed response.",
    "I appreciate you bringing this to our attention. I'll escalate this to the appropriate department.",
    "Thank you for reaching out. I'll review your request and respond within 24 hours.",
    "I'm here to help. Let me gather more information and get back to you with a solution."
  ];

  // Fetch pending escalations for Queue tab
  const fetchQueue = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await escalationApi.getPending();
      setQueue(response.data.pending || []);
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to load queue");
      console.error("Queue fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch in-progress sessions for In-Progress tab
  const fetchSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await escalationApi.getEscalation("live");
      const liveSessions = (response.data.sessions || []).map((session: any) => ({
        id: session.id || session.session_id,
        user: session.user_id || session.email || "Unknown User",
        topic: session.current_topic || "General Inquiry",
        started: session.start_time || session.login_time || new Date().toISOString(),
        chat: session.messages || [{ sender: "user", text: "Session in progress..." }],
        status: session.status || "active"
      }));
      setSessions(liveSessions);
    } catch (err: any) {
      console.error("Failed to load live sessions:", err);
      setError("Failed to load in-progress sessions");
      setSessions([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch available admins for routing
  const fetchAvailableAdmins = async () => {
    try {
      const response = await adminApi.getAvailableForRouting();
      setAvailableAdmins(response.data.available_admins || []);
    } catch (error: any) {
      console.error("Failed to fetch available admins:", error);
      alert("Failed to fetch available admins");
      setAvailableAdmins([]);
    }
  };

  useEffect(() => {
    if (tab === "Queue") {
      fetchQueue();
    } else if (tab === "In-Progress") {
      fetchSessions();
    }
  }, [tab]);

  // Accept escalation handler
  const handleAccept = async (id: string) => {
    try {
      const currentAdminEmail = localStorage.getItem('user_email') || '<EMAIL>';
      await escalationApi.assignEscalation(id, {
        assigned_to: currentAdminEmail,
        assigned_by: currentAdminEmail
      });
      
      // Move to sessions and remove from queue
      const acceptedItem = queue.find((q) => q.id === id);
      if (acceptedItem) {
        setSessions((prev) => [...prev, { ...acceptedItem, started: new Date().toISOString(), chat: [] }]);
        setQueue((prev) => prev.filter((q) => q.id !== id));
      }
      
      alert("Escalation accepted successfully");
    } catch (error: any) {
      console.error("Failed to accept escalation:", error);
      alert("Failed to accept escalation");
    }
  };
  
  // Decline escalation handler
  const handleDecline = async (id: string) => {
    try {
      // Update status to declined
      await escalationApi.replyToEscalation(id, {
        reply: "Escalation declined",
        replied_by: localStorage.getItem('user_email') || '<EMAIL>',
        reply_type: 'hr'
      });
      
      setQueue((prev) => prev.filter((q) => q.id !== id));
      alert("Escalation declined");
    } catch (error: any) {
      console.error("Failed to decline escalation:", error);
      alert("Failed to decline escalation");
    }
  };
  
  const handleClose = (id: string) => {
    setSessions((prev) => prev.filter((s) => s.id !== id));
    alert("Session closed");
  };
  
  const handleTransfer = (id: string) => {
    alert("Transfer functionality not implemented yet");
  };

  // Route handlers
  const handleRoute = (item: EscalationItem) => {
    setSelectedRouteItem(item);
    setSelectedAdmin("");
    setRouteReason("");
    setDueDate("");
    fetchAvailableAdmins();
    setShowRouteDialog(true);
  };

  const handleSendRoute = async () => {
    if (!selectedAdmin || !dueDate) {
      alert("Please select an admin and set a due date");
      return;
    }

    setRoutingLoading(true);
    try {
      const currentAdminEmail = localStorage.getItem('user_email') || '<EMAIL>';
      
      const response = await escalationApi.routeEscalation(selectedRouteItem!.id, {
        target_admin_email: selectedAdmin,
        reason: routeReason,
        due_date: dueDate,
        routed_by: currentAdminEmail
      });

      if (response.data.success) {
        alert("Query routed successfully!");
        setShowRouteDialog(false);
        setSelectedRouteItem(null);
        
        // Refresh the queue
        if (tab === "Queue") {
          fetchQueue();
        }
      } else {
        alert("Failed to route query: " + response.data.message);
      }
    } catch (error: any) {
      console.error("Failed to route query:", error);
      alert("Failed to route query. Please try again.");
    } finally {
      setRoutingLoading(false);
    }
  };

  const handleCancelRoute = () => {
    setShowRouteDialog(false);
    setSelectedRouteItem(null);
    setSelectedAdmin("");
    setRouteReason("");
    setDueDate("");
  };

  // Reply handlers
  const handleReply = (item: EscalationItem, type: 'queue' | 'session') => {
    setSelectedQueueItem({ ...item, type });
    setReplyText("");
    setShowReplyDialog(true);
  };

  const handleSendReply = async () => {
    if (!replyText.trim() || !selectedQueueItem) return;
    
    if (replyText.length > 1000) {
      alert("Reply is too long. Please keep it under 1000 characters.");
      return;
    }
    
    try {
      const isQueueItem = selectedQueueItem.type === 'queue';
      
      await escalationApi.replyToEscalation(selectedQueueItem.id, {
        reply: replyText.trim(),
        replied_by: localStorage.getItem('user_email') || '<EMAIL>',
        reply_type: 'hr'
      });
      
      alert(`Reply sent to ${selectedQueueItem.user}`);
      
      setShowReplyDialog(false);
      setSelectedQueueItem(null);
      setReplyText("");
      
      // Refresh data
      if (isQueueItem) {
        fetchQueue();
      } else {
        fetchSessions();
      }
    } catch (error: any) {
      console.error("Failed to send reply:", error);
      alert("Failed to send reply. Please try again.");
    }
  };

  const handleCancelReply = () => {
    setShowReplyDialog(false);
    setSelectedQueueItem(null);
    setReplyText("");
  };

  return (
    <div className="max-w-6xl mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Live Handover Interface</CardTitle>
          <div className="flex gap-2 mt-4">
            {TABS.map((t) => (
              <Button
                key={t}
                variant={tab === t ? "default" : "outline"}
                size="sm"
                onClick={() => setTab(t)}
              >
                {t}
              </Button>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <Skeleton className="h-80 w-full" />
          ) : error ? (
            <div className="text-red-500 p-4">{error}</div>
          ) : tab === "Queue" ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:divide-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">User</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Timestamp</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Topic</th>
                    <th className="px-4 py-2"></th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {queue.map((row, idx) => (
                    <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                      <td className="px-4 py-2 text-sm">{row.user}</td>
                      <td className="px-4 py-2 text-sm">{row.timestamp}</td>
                      <td className="px-4 py-2 text-sm">{row.topic}</td>
                      <td className="px-4 py-2 text-sm flex gap-2">
                        <Button size="sm" variant="default" onClick={() => handleAccept(row.id)}>
                          Accept
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDecline(row.id)}>
                          Decline
                        </Button>
                        <Button size="sm" variant="secondary" onClick={() => handleReply(row, 'queue')} className="bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-300">
                          Reply
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleRoute(row)} className="bg-orange-100 hover:bg-orange-200 text-orange-700 border-orange-300">
                          Route
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border dark:border-zinc-800">
                <thead className="bg-muted dark:bg-zinc-800">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">User</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Topic</th>
                    <th className="px-4 py-2 text-left text-xs font-semibold text-muted-foreground uppercase">Started</th>
                    <th className="px-4 py-2"></th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-zinc-900">
                  {sessions.map((row, idx) => (
                    <tr key={row.id || idx} className="border-b border-border dark:border-zinc-800">
                      <td className="px-4 py-2 text-sm">{row.user}</td>
                      <td className="px-4 py-2 text-sm">{row.topic}</td>
                      <td className="px-4 py-2 text-sm">{row.started}</td>
                      <td className="px-4 py-2 text-sm flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleClose(row.id)}>
                          Close
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleTransfer(row.id)}>
                          Transfer
                        </Button>
                        <Button size="sm" variant="secondary" onClick={() => handleReply(row, 'session')} className="bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-300">
                          Reply
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {/* Chat preview stub for first session */}
              {sessions.length > 0 && (
                <div className="mt-6 p-4 border rounded bg-muted dark:bg-zinc-800">
                  <div className="font-semibold mb-2">Chat Preview (stub)</div>
                  <div className="space-y-2">
                    {sessions[0].chat?.map((msg: any, i: number) => (
                      <div key={i} className={msg.sender === "admin" ? "text-right" : "text-left"}>
                        <span className={msg.sender === "admin" ? "bg-primary/20" : "bg-muted"}>
                          <span className="inline-block px-2 py-1 rounded">
                            <span className="font-semibold">{msg.sender === "admin" ? "Admin" : "User"}:</span> {msg.text}
                          </span>
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reply Dialog */}
      <Dialog open={showReplyDialog} onOpenChange={setShowReplyDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Reply to User: {selectedQueueItem?.user}
              <span className="ml-2 text-sm text-muted-foreground">
                ({selectedQueueItem?.type === 'queue' ? 'Queue Item' : 'Ongoing Session'})
              </span>
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Topic</label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {selectedQueueItem?.topic}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">
                {selectedQueueItem?.type === 'queue' ? 'Timestamp' : 'Started'}
              </label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {selectedQueueItem?.type === 'queue' ? selectedQueueItem?.timestamp : selectedQueueItem?.started}
              </div>
            </div>
            {selectedQueueItem?.type === 'session' && selectedQueueItem?.chat && (
              <div>
                <label className="text-sm font-medium">Recent Chat History</label>
                <div className="mt-1 p-3 bg-muted rounded-md max-h-32 overflow-y-auto">
                  {selectedQueueItem.chat.slice(-3).map((msg: any, i: number) => (
                    <div key={i} className="text-sm mb-1">
                      <span className="font-medium">{msg.sender === 'admin' ? 'Admin' : 'User'}:</span> {msg.text}
                    </div>
                  ))}
                </div>
              </div>
            )}
            <div>
              <label className="text-sm font-medium">Your Reply</label>
              <div className="mt-2 mb-3">
                <label className="text-xs text-muted-foreground mb-2 block">Quick Reply Templates:</label>
                <div className="flex flex-wrap gap-2">
                  {quickReplies.map((template, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => setReplyText(template)}
                      className="text-xs h-8 px-2"
                    >
                      Template {index + 1}
                    </Button>
                  ))}
                </div>
              </div>
              <Textarea
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                placeholder={`Enter your reply to ${selectedQueueItem?.user}...`}
                rows={4}
                className="mt-1"
              />
              <div className="flex justify-between items-center text-xs">
                <span className="text-muted-foreground">Maximum 1000 characters</span>
                <span className={replyText.length > 900 ? 'text-orange-600' : replyText.length > 800 ? 'text-yellow-600' : 'text-muted-foreground'}>
                  {replyText.length}/1000
                </span>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancelReply}>
                Cancel
              </Button>
              <Button onClick={handleSendReply} disabled={!replyText.trim() || replyText.length > 1000}>
                Send Reply
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Route Dialog */}
      <Dialog open={showRouteDialog} onOpenChange={setShowRouteDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              Route Query to Another Admin
              <span className="ml-2 text-sm text-muted-foreground">
                (Query from {selectedRouteItem?.user})
              </span>
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Topic</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {selectedRouteItem?.topic}
              </div>
            </div>
            <div>
              <Label>Timestamp</Label>
              <div className="mt-1 p-3 bg-muted rounded-md">
                {selectedRouteItem?.timestamp}
              </div>
            </div>
            <div>
              <Label htmlFor="admin-select">Select Admin to Route To *</Label>
              <Select value={selectedAdmin} onValueChange={setSelectedAdmin}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose an admin..." />
                </SelectTrigger>
                <SelectContent>
                  {availableAdmins.map((admin) => (
                    <SelectItem key={admin.email} value={admin.email}>
                      {admin.full_name} ({admin.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="due-date">Due Date *</Label>
              <Input
                id="due-date"
                type="datetime-local"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                min={new Date().toISOString().slice(0, 16)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="route-reason">Reason for Routing (Optional)</Label>
              <Textarea
                id="route-reason"
                value={routeReason}
                onChange={(e) => setRouteReason(e.target.value)}
                placeholder="Explain why you're routing this query..."
                rows={3}
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={handleCancelRoute}>
                Cancel
              </Button>
              <Button 
                onClick={handleSendRoute} 
                disabled={!selectedAdmin || !dueDate || routingLoading}
                className="bg-orange-600 hover:bg-orange-700"
              >
                {routingLoading ? "Routing..." : "Route Query"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LiveHandover; 