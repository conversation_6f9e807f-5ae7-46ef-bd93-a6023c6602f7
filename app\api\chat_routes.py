"""
Chat and query routes for the HR Assistant Chatbot.
Handles chat interactions, speech processing, and conversation management.
"""
import os
import time
import uuid
import traceback
import signal
from pathlib import Path
from flask import Blueprint, request, jsonify, current_app

from app.services.service_manager import ServiceManager
from src.utils.logger import get_logger
from app.middleware.error_handler import APIError
from app.middleware.logging_middleware import RequestTimer
from src.document_processing.file_processor import FileProcessor
from src.database.conversation_store import ConversationStore

logger = get_logger(__name__)

def timeout_handler(signum, frame):
    """Handle timeout signal for intent classification."""
    raise TimeoutError("Intent classification timed out")

def run_with_timeout(func, *args, timeout_seconds=10, **kwargs):
    """Run a function with a timeout."""
    import signal
    import threading
    import queue
    
    result_queue = queue.Queue()
    
    def target():
        try:
            result = func(*args, **kwargs)
            result_queue.put(('success', result))
        except Exception as e:
            result_queue.put(('error', e))
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        logger.warning(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
        return None
    
    try:
        status, result = result_queue.get_nowait()
        if status == 'error':
            return result  # Return the exception
        return result
    except queue.Empty:
        logger.warning(f"Function {func.__name__} completed but no result available")
        return None

# Create blueprint
chat_bp = Blueprint('chat', __name__)


@chat_bp.route('/query', methods=["POST"])
def query():
    """
    Main chat query endpoint.
    Processes user queries and returns AI-generated responses.
    """
    logger.info("=" * 80)
    logger.info("🔍 QUERY ENDPOINT CALLED")
    logger.info("=" * 80)
    logger.info(f"Request method: {request.method}")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request URL: {request.url}")
    logger.info(f"Request remote addr: {request.remote_addr}")
    logger.info(f"Request user agent: {request.user_agent}")
    
    # Log request data
    try:
        if request.is_json:
            data = request.json
            logger.info(f"Request JSON data: {data}")
        else:
            logger.warning("Request is not JSON!")
            logger.info(f"Request form data: {request.form}")
            logger.info(f"Request args: {request.args}")
            data = {}
    except Exception as e:
        logger.error(f"Error parsing request data: {e}")
        data = {}
    
    query_start_time = time.time()
    
    try:
        with RequestTimer("chat_query_processing"):
            user_query = data.get("query", "").strip()
            device_id = data.get("device_id", "web-client")
            files_info = data.get("files_info", [])
            response_mode = data.get("response_mode", "detailed")
            email = data.get("email")
            employee_id = data.get("employee_id")
            
            logger.info(f"Extracted data:")
            logger.info(f"  - user_query: '{user_query}'")
            logger.info(f"  - device_id: '{device_id}'")
            logger.info(f"  - files_info: {files_info}")
            logger.info(f"  - response_mode: '{response_mode}'")
            logger.info(f"  - email: '{email}'")
            logger.info(f"  - employee_id: '{employee_id}'")
            
            if not user_query:
                logger.error("❌ Query text is missing or empty")
                raise APIError(
                    message="Query text is required",
                    status_code=400
                )
            
            logger.info(f"✅ Processing query: {user_query[:100]}... | device_id: {device_id}")
            
            service_manager = ServiceManager.get_instance()
            logger.info("✅ Service manager retrieved")
            
            # Handle full-document summarization
            if _is_full_doc_summary(user_query) and files_info:
                logger.info("📄 Handling full document summary")
                return _handle_full_document_summary(user_query, files_info, service_manager)
            
            # Get intent classification
            logger.info("🧠 Classifying intent...")
            intent_result = _classify_intent(user_query, service_manager)
            logger.info(f"✅ Intent classified: {intent_result}")
            
            # Handle text extraction intent
            if intent_result.get("intent") == "text_extraction" and files_info:
                logger.info("📝 Handling text extraction")
                return _handle_text_extraction(files_info)
            
            # Check if files are processed
            if files_info:
                logger.info("📁 Validating files...")
                _validate_files_processed(files_info, service_manager)
            
            # Check vector database
            logger.info("🔍 Checking vector database...")
            chain_builder = service_manager.get_chain_builder()
            doc_count = chain_builder.get_vector_database_count() if hasattr(chain_builder, 'get_vector_database_count') else 0
            logger.info(f"📊 Vector database count: {doc_count}")
            
            if doc_count == 0:
                logger.warning("⚠️ Vector database is empty, returning fallback response")
                return _create_fallback_response(
                    "I don't have any documents loaded yet. Please upload some HR documents first so I can help you with your questions.",
                    query_start_time
                )
            
            # Handle leave balance queries
            if _is_leave_balance_query(user_query):
                logger.info("📅 Handling leave balance query")
                if not email or not employee_id:
                    logger.error("❌ Email and employee ID required for leave balance queries")
                    raise APIError(
                        message="Email and employee ID are required for leave balance queries.",
                        status_code=400
                    )
                return _handle_leave_balance_query(email, employee_id)
            
            # Process query through chain
            logger.info("🔄 Processing query through chain...")
            result = _process_query_through_chain(
                chain_builder, user_query, device_id, files_info, 
                response_mode, email, employee_id, intent_result, service_manager
            )
            logger.info(f"✅ Chain processing completed: {result.keys() if isinstance(result, dict) else 'Not a dict'}")
            
            # Save conversation
            chat_id = data.get('chat_id') or str(uuid.uuid4())
            logger.info(f"💾 Saving conversation with chat_id: {chat_id}")
            _save_conversation(
                chat_id, user_query, result, device_id, 
                query_start_time, intent_result, service_manager
            )
            
            # Format final response
            logger.info("📝 Formatting final response...")
            final_response = _format_final_response(result, query_start_time)
            logger.info(f"✅ Final response formatted, keys: {final_response.keys() if isinstance(final_response, dict) else 'Not a dict'}")
            
            logger.info("=" * 80)
            logger.info("🎉 QUERY PROCESSING COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            
            return jsonify(final_response)
            
    except Exception as e:
        logger.error("=" * 80)
        logger.error("❌ QUERY PROCESSING FAILED")
        logger.error("=" * 80)
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        logger.error("=" * 80)
        
        # Return error response
        error_response = _create_error_fallback_response(query_start_time)
        return jsonify(error_response), 500


@chat_bp.route('/speech-to-text', methods=["POST"])
def speech_to_text_api():
    """
    Convert speech to text using Whisper or Google API.
    Handles both uploaded audio files and microphone input.
    """
    try:
        with RequestTimer("speech_to_text_processing"):
            # Get parameters
            use_whisper = request.args.get("use_whisper", "true").lower() == "true"
            audio_data = request.files.get("audio")

            service_manager = ServiceManager.get_instance()
            stt = service_manager.get_speech_to_text(use_whisper=use_whisper)

            if audio_data:
                # Process uploaded audio file
                import tempfile
                import soundfile as sf
                import numpy as np
                import os
                
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                    audio_data.save(temp_file.name)
                    
                    # Load audio data
                    audio_array, sample_rate = sf.read(temp_file.name)
                    
                    # Convert to mono if stereo
                    if len(audio_array.shape) > 1:
                        audio_array = audio_array.mean(axis=1)
                    
                    # Convert to float32 and normalize
                    audio_array = audio_array.astype(np.float32)
                    
                    # Clean up temp file
                    os.unlink(temp_file.name)
                
                # Transcribe using the specified method
                result = stt.transcribe(audio_array)
                text = result.get("text", "")
                
            else:
                # Use microphone recording
                text = stt.recognize_speech()

            return jsonify({
                "text": text,
                "method": "whisper" if use_whisper else "google",
                "success": True
            })

    except Exception as e:
        logger.exception(f"Speech-to-text failed: {e}")
        raise APIError(
            message=f"Speech-to-text processing failed: {str(e)}",
            status_code=500
        )


@chat_bp.route('/clear-history', methods=["POST"])
def clear_history():
    """
    Clear conversation history for a device.
    Removes all stored conversation data for the specified device.
    """
    try:
        data = request.json
        device_id = data.get("device_id")
        
        if not device_id:
            raise APIError(
                message="Device ID is required",
                status_code=400
            )

        service_manager = ServiceManager.get_instance()
        history_manager = service_manager.get_history_manager()
        session_history = service_manager.get_session_history()
        
        history_manager.clear_history(device_id)

        if device_id in session_history:
            del session_history[device_id]

        return jsonify({
            "success": True,
            "message": f"History cleared for device {device_id}"
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Clear history failed: {e}")
        raise APIError(
            message=f"Failed to clear history: {str(e)}",
            status_code=500
        )


@chat_bp.route('/chats/<chat_id>', methods=["GET"])
def get_chat(chat_id):
    """
    Get paginated messages for a specific chat.
    Returns conversation history with pagination support.
    """
    try:
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 20))

        service_manager = ServiceManager.get_instance()
        history_manager = service_manager.get_history_manager()
        
        messages = history_manager.get_chat_messages(chat_id, page, page_size)
        total_messages = history_manager.get_chat_message_count(chat_id)

        return jsonify({
            "success": True,
            "messages": messages,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_messages": total_messages,
                "total_pages": (total_messages + page_size - 1) // page_size
            }
        })

    except ValueError as e:
        raise APIError(
            message="Invalid pagination parameters",
            status_code=400,
            details=str(e)
        )
    except Exception as e:
        logger.exception(f"Get chat failed: {e}")
        raise APIError(
            message=f"Failed to get chat messages: {str(e)}",
            status_code=500
        )


@chat_bp.route('/chats/<chat_id>/count', methods=["GET"])
def get_chat_message_count_api(chat_id):
    """
    Get total number of messages for a specific chat.
    Returns message count for pagination calculations.
    """
    try:
        service_manager = ServiceManager.get_instance()
        history_manager = service_manager.get_history_manager()
        
        total_messages = history_manager.get_chat_message_count(chat_id)
        
        return jsonify({
            "success": True,
            "count": total_messages
        })
        
    except Exception as e:
        logger.exception(f"Get chat count failed: {e}")
        raise APIError(
            message=f"Failed to get chat message count: {str(e)}",
            status_code=500
        )


# Helper functions for chat processing

def _is_full_doc_summary(query: str) -> bool:
    """Check if query is requesting full document summarization."""
    query_lower = query.lower()
    summary_keywords = [
        "summarize the file", "summarise the file", "summarize all", "summarise all",
        "summarize the text from faqs file", "summarise the text from faqs file",
        "summarize entire file", "summarise entire file"
    ]
    return any(keyword in query_lower for keyword in summary_keywords)


def _is_leave_balance_query(query: str) -> bool:
    """Check if query is requesting leave balance information."""
    query_lower = query.lower()
    leave_keywords = ["leave balance", "how many leaves", "my leave", "leaves in my account"]
    return any(keyword in query_lower for keyword in leave_keywords)


def _classify_intent(query: str, service_manager) -> dict:
    """Classify user intent using the intent classifier."""
    from src.config import INTENT_CLASSIFIER_ENABLED, INTENT_CLASSIFIER_FALLBACK_ONLY
    
    # Check if intent classifier is disabled or fallback-only mode
    if not INTENT_CLASSIFIER_ENABLED or INTENT_CLASSIFIER_FALLBACK_ONLY:
        logger.info("Intent classifier disabled or in fallback-only mode, using keyword-based classification")
        return _fallback_intent_classification(query)
    
    # Check if intent classifier is disabled via environment variable
    if os.getenv('DISABLE_INTENT_CLASSIFIER', 'false').lower() == 'true':
        logger.info("Intent classifier disabled via environment variable, using keyword-based classification")
        return _fallback_intent_classification(query)
    
    try:
        intent_classifier = service_manager.get_intent_classifier()
        
        if intent_classifier:
            try:
                # Verify model is actually loaded and ready
                if not hasattr(intent_classifier, 'model') or intent_classifier.model is None:
                    logger.warning("Intent classifier model not loaded, using fallback")
                    return _fallback_intent_classification(query)
                
                # Model is pre-loaded during initialization, so this should be fast
                logger.info("Starting intent classification...")
                
                # Run intent classification with timeout protection
                intent_result = run_with_timeout(
                    intent_classifier.classify_intent, 
                    query, 
                    timeout_seconds=15
                )
                
                if intent_result is None:
                    logger.warning("Intent classification timed out, using fallback")
                    return _fallback_intent_classification(query)
                elif isinstance(intent_result, Exception):
                    logger.error(f"Intent classification failed: {intent_result}")
                    return _fallback_intent_classification(query)
                
                logger.info(f"Intent classification successful: {intent_result.get('intent', 'unknown')}")
                return intent_result
            except Exception as e:
                logger.error(f"Intent classification failed: {e}")
                import traceback
                logger.error(f"Classification error traceback: {traceback.format_exc()}")
                logger.info("Falling back to keyword-based classification")
                return _fallback_intent_classification(query)
        else:
            logger.warning("Intent classifier not available, using fallback")
            
    except Exception as e:
        logger.error(f"Failed to get intent classifier: {e}")
        import traceback
        logger.error(f"Service manager error traceback: {traceback.format_exc()}")
        logger.info("Using fallback classification")

    # Fallback classification
    return _fallback_intent_classification(query)


def _fallback_intent_classification(query: str) -> dict:
    """Fallback intent classification using keyword matching."""
    query_lower = query.lower()

    # Define keyword mappings with better coverage
    intent_mappings = [
        (["dress code", "dress", "attire", "clothing", "uniform", "what to wear", "wear", "outfit", "business casual", "formal", "casual"], "dress_code", 0.9),
        (["leave", "vacation", "time off", "holiday", "sick leave", "pto", "personal time", "absence"], "leave_inquiry", 0.8),
        (["policy", "policies", "rules", "guidelines", "procedure", "protocol"], "policy_inquiry", 0.7),
        (["benefits", "insurance", "health", "dental", "vision", "coverage"], "benefits_inquiry", 0.8),
        (["salary", "pay", "compensation", "bonus", "raise", "wage"], "compensation_inquiry", 0.8),
        (["onboarding", "orientation", "new hire", "training", "first day"], "onboarding_inquiry", 0.8),
        (["extract", "full text", "full content", "get document", "show complete text"], "text_extraction", 0.9)
    ]

    for keywords, intent, confidence in intent_mappings:
        if any(keyword in query_lower for keyword in keywords):
            matched_keywords = [kw for kw in keywords if kw in query_lower]
            return {
                "intent": intent,
                "confidence": confidence,
                "query": query,
                "keywords": matched_keywords,
                "calibration_info": {},
                "error": None
            }

    # Special handling for dress code queries that might not match exact keywords
    if any(word in query_lower for word in ["company", "work", "office", "professional"]) and any(word in query_lower for word in ["wear", "dress", "clothes", "attire"]):
        return {
            "intent": "dress_code",
            "confidence": 0.85,
            "query": query,
            "keywords": ["company dress code"],
            "calibration_info": {},
            "error": None
        }
    
    # Enhanced dress code detection for your specific query
    if "company dress code policy" in query_lower or "dress code policy" in query_lower:
        return {
            "intent": "dress_code",
            "confidence": 0.95,
            "query": query,
            "keywords": ["company dress code policy"],
            "calibration_info": {},
            "error": None
        }

    return {
        "intent": "general_inquiry",
        "confidence": 0.6,
        "query": query,
        "keywords": [],
        "calibration_info": {},
        "error": None
    }


def _handle_full_document_summary(query: str, files_info: list, service_manager) -> dict:
    """Handle full document summarization requests."""
    try:
        file_name = files_info[0].get("name")
        if not file_name:
            raise APIError(
                message="No file name provided for summarization",
                status_code=400
            )

        vector_search = service_manager.get_vector_search()

        # Check if method exists
        if not hasattr(vector_search.vector_store, 'get_all_chunks_for_file'):
            raise APIError(
                message="Vector store does not support full-document chunk retrieval.",
                status_code=500
            )

        all_chunks = vector_search.vector_store.get_all_chunks_for_file(file_name)
        if not all_chunks:
            raise APIError(
                message=f"No chunks found for file {file_name}. Please ensure it is processed.",
                status_code=400
            )

        full_text = '\n\n'.join(chunk['content'] for chunk in all_chunks if chunk.get('content'))
        if not full_text.strip():
            raise APIError(
                message=f"File {file_name} has no extractable content.",
                status_code=400
            )

        # Generate summary
        chain_builder = service_manager.get_chain_builder()
        summary_raw = service_manager.run_async_in_sync(
            chain_builder.summarize(full_text, source_file=file_name)
        )

        # Format output
        if not summary_raw.strip().startswith("##"):
            summary_final = f"## Document Summary\n\n" + summary_raw.strip()
        else:
            summary_final = summary_raw.strip()

        return jsonify({
            "summary": summary_final,
            "used_fallback": False,
            "retrieved_docs": all_chunks,
            "raw_output": summary_raw,
            "language": "en",
            "response_time": 0,
            "document_count": len(all_chunks)
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Full document summary failed: {e}")
        raise APIError(
            message="Full-document summary failed",
            status_code=500,
            details=str(e)
        )


def _handle_text_extraction(files_info: list) -> dict:
    """Handle text extraction requests."""
    try:
        file_name = files_info[0].get("name")
        if not file_name:
            raise APIError(
                message="No file name provided for text extraction",
                status_code=400
            )

        raw_dir = Path(current_app.root_path) / "data" / "raw"
        file_path = raw_dir / file_name

        if not file_path.exists():
            raise APIError(
                message=f"File {file_name} not found in raw directory.",
                status_code=400
            )

        document = FileProcessor.process_file(file_path)
        content = document.get("content", "")

        return jsonify({
            "summary": content,
            "used_fallback": False,
            "retrieved_docs": [document],
            "raw_output": content,
            "language": "en",
            "response_time": 0,
            "document_count": 1
        })

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Text extraction failed: {e}")
        raise APIError(
            message="Text extraction failed",
            status_code=500,
            details=str(e)
        )


def _validate_files_processed(files_info: list, service_manager) -> None:
    """Validate that all files have been processed."""
    pipeline = service_manager.get_training_pipeline()

    for file_info in files_info:
        file_name = file_info.get("name")
        if file_name and not pipeline.is_file_processed(file_name):
            raise APIError(
                message=f"File {file_name} not processed yet. Please wait for processing to complete.",
                status_code=400
            )


def _handle_leave_balance_query(email: str, employee_id: str) -> dict:
    """Handle leave balance queries by calling the leave balance endpoint."""
    from flask import current_app

    # This would typically call the leave balance service
    # For now, we'll redirect to the existing endpoint logic
    with current_app.test_request_context(f"/api/leave-balance?email={email}&employee_id={employee_id}"):
        from app.api.admin_routes import get_leave_balance
        return get_leave_balance()


def _process_query_through_chain(chain_builder, user_query, device_id, files_info,
                                response_mode, email, employee_id, intent_result, service_manager) -> dict:
    """Process query through the chain builder."""
    try:
        if hasattr(chain_builder, 'run_chain_sync'):
            logger.info("Using synchronous chain execution")
            result = chain_builder.run_chain_sync(
                user_query, device_id, files_info=files_info,
                response_mode=response_mode, email=email,
                employee_id=employee_id, intent_result=intent_result
            )
        else:
            logger.info("Using asynchronous chain execution")
            result = service_manager.run_async_in_sync(
                chain_builder.run_chain(
                    user_query, device_id, files_info=files_info,
                    response_mode=response_mode, email=email,
                    employee_id=employee_id, intent_result=intent_result
                )
            )

        return result

    except Exception as e:
        logger.exception(f"Chain execution failed: {e}")
        # Return fallback response
        return {
            "content": "I'm sorry, I'm having trouble processing your request right now. Please try again in a moment.",
            "language": "en",
            "sources": [],
            "intent": "error",
            "intent_confidence": 0.0,
            "response_mode": "error"
        }


def _save_conversation(chat_id: str, user_query: str, result: dict, device_id: str,
                      query_start_time: float, intent_result: dict, service_manager) -> None:
    """Save conversation to the database."""
    try:
        conversation_store = ConversationStore()

        response_content = result.get("content", "") if isinstance(result, dict) else ""
        language = result.get("language", "en") if isinstance(result, dict) else "en"
        intent = result.get("intent") if isinstance(result, dict) and result.get("intent") else intent_result.get("intent", "unknown")

        conversation_store.save_conversation(
            chat_id=chat_id,
            user_query=user_query,
            assistant_response=response_content,
            language=language,
            device_id=device_id,
            query_start_time=query_start_time,
            response_end_time=time.time(),
            intent=intent
        )

    except Exception as e:
        logger.exception(f"Failed to save conversation: {e}")
        # Don't raise error - conversation saving failure shouldn't break the response


def _format_final_response(result: dict, query_start_time: float) -> dict:
    """Format the final response for the client."""
    response_content = result.get("content", "") if isinstance(result, dict) else ""
    sources = result.get("sources", []) if isinstance(result, dict) else []
    raw_model_output = result.get("raw_output", response_content) if isinstance(result, dict) else response_content
    retrieved_doc_count = result.get("retrieved_doc_count", len(sources)) if isinstance(result, dict) else len(sources)

    # Check if response is adequate
    used_fallback = False
    fallback_message = "I'm sorry, I couldn't find relevant information in the available documents. Please ensure your documents are properly uploaded and processed."

    if not response_content or len(response_content.strip()) < 20:
        final_summary = fallback_message
        used_fallback = True
    else:
        final_summary = response_content.strip()
        used_fallback = False

    return {
        "summary": final_summary,
        "used_fallback": used_fallback,
        "retrieved_docs": sources,
        "raw_output": raw_model_output,
        "language": result.get("language", "en") if isinstance(result, dict) else "en",
        "response_time": round(time.time() - query_start_time, 2),
        "document_count": retrieved_doc_count,
        "intent": result.get("intent", "unknown") if isinstance(result, dict) else "unknown",
        "intent_confidence": result.get("intent_confidence", 0.0) if isinstance(result, dict) else 0.0,
        "response_mode": result.get("response_mode", "unknown") if isinstance(result, dict) else "unknown"
    }


def _create_fallback_response(message: str, query_start_time: float, intent: str = "unknown") -> dict:
    """Create a standardized fallback response."""
    return jsonify({
        "summary": message,
        "used_fallback": True,
        "retrieved_docs": [],
        "raw_output": "No documents available",
        "language": "en",
        "response_time": round(time.time() - query_start_time, 2),
        "document_count": 0,
        "intent": intent,
        "intent_confidence": 0.0,
        "response_mode": "fallback"
    })


def _create_dress_code_fallback_response(query_start_time: float) -> dict:
    """Create fallback response for dress code queries."""
    dress_code_response = """Based on Ziantrix Technology Solutions Dress Code Policy:

**General Guidelines:**
- Maintain clean, neat, and professional appearance during office hours
- Dress appropriately for your role (client-facing vs internal)

**Weekdays (Monday-Thursday):**
**For Men:**
- Formal collared shirts (tucked-in preferred)
- Formal trousers (no jeans)
- Closed formal leather shoes (black or brown)
- Neatly groomed hair and clean nails

**For Women:**
- Formal western wear: shirts/blouses with trousers or pencil skirts
- Formal Indian wear: sarees, salwar suits, or kurtas in sober tones
- Formal closed or peep-toe footwear
- Moderate makeup, long hair tied back

**Casual Fridays:**
- Business casual permitted (no client meetings)
- Jeans without tears, casual shirts/tops
- Clean sneakers or loafers
- Smart casual ethnic wear

**Not Permitted:**
- Shorts, ripped jeans, sleeveless tops, tank tops
- Flip-flops, hoodies with logos
- Offensive graphic prints

**Personal Hygiene:**
- Daily bathing and deodorant mandatory
- Oral hygiene and fresh breath
- Clean, well-ironed clothes
- Avoid excessive jewelry

**Special Cases:**
- Formal business attire required for client meetings
- Safety gear where applicable

Non-compliance may result in warnings or disciplinary action."""

    return jsonify({
        "summary": dress_code_response,
        "used_fallback": True,
        "retrieved_docs": [],
        "raw_output": "Dress code policy fallback response",
        "language": "en",
        "response_time": round(time.time() - query_start_time, 2),
        "document_count": 0,
        "intent": "dress_code",
        "intent_confidence": 0.8,
        "response_mode": "fallback"
    })


def _create_error_fallback_response(query_start_time: float) -> dict:
    """Create fallback response for errors."""
    return jsonify({
        "summary": "I'm having trouble processing your request right now. This might be due to missing documents or a temporary issue with the AI service. Please try uploading some HR documents first, or try again in a moment.",
        "used_fallback": True,
        "retrieved_docs": [],
        "raw_output": "Error occurred during processing",
        "language": "en",
        "response_time": round(time.time() - query_start_time, 2),
        "document_count": 0,
        "intent": "error",
        "intent_confidence": 0.0,
        "response_mode": "error"
    })


@chat_bp.route('/clarify', methods=["POST"])
def clarify_message():
    """
    Generate clarification for a selected portion of a previous message.
    Provides detailed explanations for specific parts of assistant responses.
    """
    logger.info("/api/clarify called")

    try:
        with RequestTimer("clarification_processing"):
            data = request.json
            selected_text = data.get("selected_text", "").strip()
            previous_message = data.get("previous_message", "").strip()
            clarification_query = data.get("clarification_query", "").strip()
            device_id = data.get("device_id", "web-client")
            language = data.get("language", "en")
            response_mode = data.get("response_mode", "concise")

            # Validate required fields
            if not selected_text:
                raise APIError(
                    message="Please provide the text you want clarification about.",
                    status_code=400,
                    error_type="Missing selected_text"
                )

            if not previous_message:
                raise APIError(
                    message="Previous message context is required for clarification.",
                    status_code=400,
                    error_type="Missing previous_message"
                )

            logger.info(f"Clarification request - selected_text: '{selected_text[:50]}...', device_id: {device_id}")

            service_manager = ServiceManager.get_instance()
            chain_builder = service_manager.get_chain_builder()

            # Generate clarification
            clarification_result = chain_builder.run_clarification_sync(
                selected_text=selected_text,
                previous_message=previous_message,
                clarification_query=clarification_query,
                device_id=device_id,
                language=language,
                response_mode=response_mode
            )

            # Add clarification to history
            history_manager = service_manager.get_history_manager()
            history_manager.add_interaction(
                user_query=f"Clarify: '{selected_text}'",
                assistant_response=clarification_result["content"],
                language=language,
                device_id=device_id
            )

            logger.info("Clarification generated successfully")
            return jsonify(clarification_result)

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Clarification failed: {e}")
        raise APIError(
            message="Clarification processing failed",
            status_code=500,
            details=str(e)
        )


@chat_bp.route('/generate-followup', methods=["POST"])
def generate_followup_question():
    """
    Generate a follow-up question based on selected text from assistant message.
    Helps users explore topics in more depth.
    """
    logger.info("/api/generate-followup called")

    try:
        with RequestTimer("followup_generation"):
            data = request.json
            selected_text = data.get("selected_text", "").strip()
            full_assistant_message = data.get("full_assistant_message", "").strip()
            previous_user_query = data.get("previous_user_query", "").strip()
            device_id = data.get("device_id", "web-client")
            language = data.get("language", "en")

            if not selected_text:
                raise APIError(
                    message="Please select some text to generate a follow-up question.",
                    status_code=400,
                    error_type="Missing selected_text"
                )

            logger.info(f"Follow-up generation - Selected text: {selected_text[:100]}...")

            # Build context for follow-up generation
            context_parts = []
            if previous_user_query:
                context_parts.append(f"Original user question: {previous_user_query}")
            context_parts.append(f"Assistant's response: {full_assistant_message}")
            context_parts.append(f"Selected portion: {selected_text}")

            context = "\n\n".join(context_parts)

            service_manager = ServiceManager.get_instance()
            chain_builder = service_manager.get_chain_builder()

            # Generate follow-up question
            followup_result = chain_builder.generate_followup_question_sync(
                selected_text=selected_text,
                context=context,
                language=language
            )

            logger.info("Follow-up question generated successfully")
            return jsonify(followup_result)

    except APIError:
        raise
    except Exception as e:
        logger.exception(f"Follow-up generation failed: {e}")
        raise APIError(
            message="Follow-up question generation failed",
            status_code=500,
            details=str(e)
        )
