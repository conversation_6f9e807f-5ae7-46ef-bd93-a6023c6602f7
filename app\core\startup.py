"""
Application startup and initialization module.
Handles environment validation, service initialization, and graceful startup/shutdown.
"""
import os
import sys
import signal
import threading
from typing import Optional
from datetime import datetime

from src.utils.logger import get_logger, log_error
from app.services.service_manager import ServiceManager
from src.monitoring import initialize_apm

logger = get_logger(__name__)

# Global flag to prevent multiple initializations
_startup_completed = False
_startup_lock = threading.Lock()


def validate_environment() -> None:
    """
    Validate required environment variables and configuration.
    Raises ValueError if critical configuration is missing.
    """
    logger.info("Validating environment configuration...")
    
    required_vars = {
        'GROQ_API_KEY': 'GROQ API key for LLM functionality',
        'FLASK_SECRET_KEY': 'Flask secret key for session management'
    }
    
    missing_vars = []
    for var_name, description in required_vars.items():
        if not os.getenv(var_name):
            missing_vars.append(f"{var_name} ({description})")
    
    if missing_vars:
        error_msg = f"Missing required environment variables:\n" + "\n".join(f"  - {var}" for var in missing_vars)
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    logger.info("Environment validation completed successfully")


def initialize_services() -> ServiceManager:
    """
    Initialize all application services using the service manager.
    Returns the configured service manager instance.
    """
    logger.info("Initializing application services...")

    try:
        # Get service manager instance (singleton)
        service_manager = ServiceManager.get_instance()

        # Initialize core services
        logger.info("Initializing core services...")

        # Initialize intent classifier with async warm-up and retry mechanism
        logger.info("Initializing intent classifier with async warm-up...")
        _initialize_intent_classifier_async(service_manager)

        # Initialize entity extractor with preloading
        logger.info("Initializing entity extractor with preloading...")
        _initialize_entity_extractor_async(service_manager)
        
        # Initialize chain builder (depends on context builder)
        service_manager.get_chain_builder()

        # Initialize history manager
        service_manager.get_history_manager()

        # Initialize payroll service
        logger.info("Initializing payroll service...")
        service_manager.get_payroll_service()

        # Initialize email service if configured
        email_service = service_manager.get_email_service()
        if email_service:
            logger.info("Email service initialized")
        else:
            logger.info("Email service not configured - escalation emails disabled")
        
        # Initialize training pipeline
        try:
            pipeline = service_manager.get_training_pipeline()
            service_manager.get_app_state()["pipeline"] = pipeline
            logger.info("Training pipeline initialized")
        except Exception as e:
            logger.warning(f"Training pipeline initialization failed: {e}")
            service_manager.get_app_state()["pipeline"] = None
        
        logger.info("Service initialization completed successfully")
        return service_manager
        
    except Exception as e:
        log_error(e, "service initialization")
        raise


def initialize_monitoring() -> Optional[object]:
    """
    Initialize application performance monitoring.
    Returns APM instance if successful, None otherwise.
    """
    logger.info("Initializing application monitoring...")
    
    try:
        apm = initialize_apm()
        if apm:
            logger.info("Application monitoring initialized successfully")
        else:
            logger.warning("Application monitoring initialization failed - continuing without monitoring")
        return apm
    except Exception as e:
        log_error(e, "monitoring initialization")
        logger.warning("Application monitoring initialization failed - continuing without monitoring")
        return None


def setup_signal_handlers() -> None:
    """Setup signal handlers for graceful shutdown."""
    logger.info("Setting up signal handlers...")
    
    def handle_shutdown_signal(sig, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received shutdown signal {sig}, initiating graceful shutdown...")
        # Signal the main thread to shutdown
        os._exit(0)
    
    # Register signal handlers
    signal.signal(signal.SIGTERM, handle_shutdown_signal)
    signal.signal(signal.SIGINT, handle_shutdown_signal)
    
    logger.info("Signal handlers configured successfully")


def startup_application() -> tuple[object, ServiceManager]:
    """
    Complete application startup sequence.
    Returns tuple of (apm_instance, service_manager).
    """
    global _startup_completed
    
    with _startup_lock:
        if _startup_completed:
            logger.warning("Startup already completed, returning existing instances")
            # Return existing instances if startup was already done
            service_manager = ServiceManager.get_instance()
            # Note: APM might not be available here, but that's okay for this case
            return None, service_manager
            
        logger.info("Starting application startup sequence...")
        
        try:
            # Setup signal handlers
            setup_signal_handlers()
            
            # Validate environment
            validate_environment()
            
            # Initialize monitoring
            apm = initialize_monitoring()
            
            # Initialize services
            service_manager = initialize_services()
            
            # Health check
            health_status = health_check_services(service_manager)
            if health_status['overall_status'] == 'healthy':
                logger.info("Application startup completed successfully - all services healthy")
            else:
                logger.warning(f"Application startup completed with warnings - {health_status['overall_status']}")
            
            _startup_completed = True
            return apm, service_manager
            
        except Exception as e:
            log_error(e, "application startup")
            logger.error("Application startup failed - shutting down")
            raise


def _initialize_intent_classifier_async(service_manager):
    """Initialize intent classifier with async warm-up and retry mechanism."""
    import threading
    import time
    from src.utils.async_task_manager import AsyncTaskManager

    def _load_intent_classifier():
        """Load intent classifier with retry mechanism."""
        max_retries = 3
        retry_delay = 2.0

        for attempt in range(max_retries):
            try:
                logger.info(f"Intent classifier loading attempt {attempt + 1}/{max_retries}")

                # Check if disabled via environment variable
                import os
                if os.getenv('DISABLE_INTENT_CLASSIFIER', 'false').lower() == 'true':
                    logger.info("Intent classifier disabled via environment variable")
                    service_manager.get_app_state()["intent_classifier"] = None
                    service_manager.get_app_state()["intent_classifier_ready"] = False
                    return

                # Initialize with timeout
                intent_classifier = service_manager.get_intent_classifier()

                if intent_classifier and hasattr(intent_classifier, 'model') and intent_classifier.model is not None:
                    # Verify model is ready by doing a test classification
                    test_result = intent_classifier.classify_intent("test query")
                    if test_result and test_result.get('intent'):
                        service_manager.get_app_state()["intent_classifier"] = intent_classifier
                        service_manager.get_app_state()["intent_classifier_ready"] = True
                        logger.info("✅ Intent classifier loaded and verified successfully")
                        return

                logger.warning(f"Intent classifier not ready on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 1.5  # Exponential backoff

            except Exception as e:
                logger.error(f"Intent classifier loading attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 1.5
                else:
                    logger.error("All intent classifier loading attempts failed")

        # Fallback: disable intent classifier
        logger.warning("Intent classifier initialization failed after all retries - using fallback only")
        service_manager.get_app_state()["intent_classifier"] = None
        service_manager.get_app_state()["intent_classifier_ready"] = False

    # Start async loading
    service_manager.get_app_state()["intent_classifier_ready"] = False
    thread = threading.Thread(target=_load_intent_classifier, daemon=True)
    thread.start()
    logger.info("Intent classifier async initialization started")


def _initialize_entity_extractor_async(service_manager):
    """Initialize entity extractor with preloading."""
    import threading

    def _load_entity_extractor():
        """Load entity extractor with error handling."""
        try:
            logger.info("Loading entity extractor...")

            # Get entity extractor and force model loading
            from src.ner.entity_extractor import EntityExtractor
            entity_extractor = EntityExtractor()
            entity_extractor.load_model()  # Force load the spaCy model

            # Verify model is loaded
            if entity_extractor.nlp is not None:
                # Test extraction
                test_result = entity_extractor.extract_entities(["test query"])
                if test_result:
                    service_manager.get_app_state()["entity_extractor"] = entity_extractor
                    service_manager.get_app_state()["entity_extractor_ready"] = True
                    logger.info("✅ Entity extractor loaded and verified successfully")
                    return

            logger.error("Entity extractor model failed to load properly")
            service_manager.get_app_state()["entity_extractor"] = None
            service_manager.get_app_state()["entity_extractor_ready"] = False

        except Exception as e:
            logger.error(f"Entity extractor initialization failed: {e}")
            service_manager.get_app_state()["entity_extractor"] = None
            service_manager.get_app_state()["entity_extractor_ready"] = False

    # Start async loading
    service_manager.get_app_state()["entity_extractor_ready"] = False
    thread = threading.Thread(target=_load_entity_extractor, daemon=True)
    thread.start()
    logger.info("Entity extractor async initialization started")


def health_check_services(service_manager: ServiceManager) -> dict:
    """
    Perform health check on all application services.
    Returns health status dictionary.
    """
    logger.info("Performing service health check...")
    
    health_status = {
        'overall_status': 'healthy',
        'services': {},
        'timestamp': None
    }
    
    try:
        # Check core services
        services_to_check = [
            ('intent_classifier', lambda: service_manager.get_intent_classifier()),
            ('chain_builder', lambda: service_manager.get_chain_builder()),
            ('history_manager', lambda: service_manager.get_history_manager()),
            ('vector_store', lambda: service_manager.get_vector_store()),
        ]
        
        for service_name, check_func in services_to_check:
            try:
                service = check_func()
                if service:
                    health_status['services'][service_name] = {
                        'status': 'healthy',
                        'message': 'Service operational'
                    }
                else:
                    health_status['services'][service_name] = {
                        'status': 'degraded',
                        'message': 'Service not available'
                    }
                    health_status['overall_status'] = 'degraded'
            except Exception as e:
                health_status['services'][service_name] = {
                    'status': 'unhealthy',
                    'message': f'Service error: {str(e)}'
                }
                health_status['overall_status'] = 'unhealthy'
        
        # Check database connectivity
        try:
            # Basic database check
            db_status = service_manager.get_app_state().get('database_status', 'unknown')
            health_status['services']['database'] = {
                'status': 'healthy' if db_status == 'connected' else 'degraded',
                'message': f'Database status: {db_status}'
            }
        except Exception as e:
            health_status['services']['database'] = {
                'status': 'unhealthy',
                'message': f'Database error: {str(e)}'
            }
            health_status['overall_status'] = 'unhealthy'
        
        health_status['timestamp'] = datetime.now().isoformat()
        
        logger.info(f"Health check completed - Overall status: {health_status['overall_status']}")
        return health_status
        
    except Exception as e:
        log_error(e, "health check")
        health_status['overall_status'] = 'error'
        health_status['error'] = str(e)
        return health_status


def shutdown_application(service_manager: ServiceManager) -> None:
    """
    Gracefully shutdown the application and cleanup resources.
    """
    logger.info("Initiating application shutdown...")
    
    try:
        # Cleanup services
        if service_manager:
            service_manager.cleanup()
            logger.info("Service cleanup completed")
        
        # Additional cleanup tasks can be added here
        
        logger.info("Application shutdown completed successfully")
        
    except Exception as e:
        log_error(e, "application shutdown")
        logger.error("Error during application shutdown")
    finally:
        logger.info("Application shutdown sequence finished")
